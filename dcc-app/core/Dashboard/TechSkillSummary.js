import { Card, CardContent } from "@core/components/ui/card";

const TechSkillSummary = ({ filteredTechSkills, userRole }) => {
  return (
    <div className="flex flex-1 flex-col gap-2 p-0">
      <div className="grid grid-cols-[10%_10%_10%_10%_10%_10%_10%_10%_10%_10%]">
        {filteredTechSkills.map((skill) => (
          <Card
            key={skill.skill_name}
            className="min-w-8 bg-dccblue text-primary-foreground rounded-tl-xl rounded-tr-xl"
          >
            <CardContent className={"font-semibold text-sm text-center p-2"}>
              {skill.skill_name}
            </CardContent>
          </Card>
        ))}
        {filteredTechSkills.map((skill) => {
          const roleMapping = skill.role_mapping.find(
            (mapping) => mapping.role_id === userRole
          );
          const level = roleMapping ? roleMapping.skill_level : null;

          const getCardStyle = (level) => {
            switch (level) {
              case 1:
                return { colour: "bg-dcclightgrey", name: "Knowledgable" };
              case 2:
                return {
                  colour: "bg-dccorange",
                  name: "Supported Practitionerst",
                };
              case 3:
                return {
                  colour: "bg-dccgreen",
                  name: "Independent Practitioner",
                };
              case 4:
                return { colour: "bg-dcclightblue", name: "Expert" };
              default:
                return "bg-gray-100";
            }
          };

          return (
            <Card
              key={skill.skill_name}
              className={`min-w-8 ${
                getCardStyle(level).colour
              } text-primary-foreground rounded-bl-xl rounded-br-xl`}
            >
              <CardContent className="text-xs text-center tracking-tight text-white font-bold p-0">
                {getCardStyle(level).name}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default TechSkillSummary;
