import { Card, CardContent } from "@core/components/ui/card";

const TSkillsLibHeader = () => (
  <>
    <Card className="invisible min-w-8 bg-[#5c2071] text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Think
      </CardContent>
    </Card>

    <Card className="min-w-8  bg-[#9ca299] text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Knowledgable
      </CardContent>
    </Card>

    <Card className="min-w-8  bg-dccorange text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Supported Practitioner
      </CardContent>
    </Card>

    <Card className="min-w-8  bg-[#95c11f] text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Independent Practitioner
      </CardContent>
    </Card>

    <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
      <CardContent className={"font-extrabold text-1xl text-center"}>
        Expert
      </CardContent>
    </Card>
  </>
);

export default TSkillsLibHeader;
