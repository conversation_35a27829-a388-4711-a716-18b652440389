import { useState } from "react";
import { Card, CardContent } from "@core/components/ui/card";
import { Checkbox } from "@core/components/ui/checkbox";
import { Label } from "@core/components/ui/label";

const MapperCard = ({ desc, level, isChecked, handleOnChange }) => {
  return (
    <Label className="hover:bg-accent/50 flex items-start gap-3 border p-3 has-[[aria-checked=true]]:border-yellow-500 has-[[aria-checked=true]]:bg-yellow-50 dark:has-[[aria-checked=true]]:border-yellow-500 dark:has-[[aria-checked=true]]:bg-yellow-400">
      <Checkbox
        id={1}
        type="checkbox"
        checked={isChecked}
        onCheckedChange={() => handleOnChange(level)}
        className="data-[state=checked]:border-yellow-500 data-[state=checked]:bg-yellow-500 data-[state=checked]:text-white dark:data-[state=checked]:border-yellow-500 dark:data-[state=checked]:bg-yellow-500"
      />
      <div className="inline-block align-middle">{desc}</div>
    </Label>
  );
};

const MapperBSkillRow = ({
  behavioural_sub_skills,
  handleOnChange,
  lvl1,
  lvl2,
  lvl3,
  lvl4,
  lvl5,
}) => {
  // put them in index order here
  behavioural_sub_skills.sort((a, b) => a.index - b.index);

  return (
    <>
      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          <div className="inline-block align-middle">
            {behavioural_sub_skills[0].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <MapperCard
        desc={behavioural_sub_skills[0].level_1_description}
        level={1}
        isChecked={lvl1}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[0].level_2_description}
        level={2}
        isChecked={lvl2}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[0].level_3_description}
        level={3}
        isChecked={lvl3}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[0].level_4_description}
        level={4}
        isChecked={lvl4}
        handleOnChange={handleOnChange}
      />

      <MapperCard
        desc={behavioural_sub_skills[0].level_5_description}
        level={5}
        isChecked={lvl5}
        handleOnChange={handleOnChange}
      />

      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          {behavioural_sub_skills[1].sub_skill_name}
        </CardContent>
      </Card>

      <MapperCard
        desc={behavioural_sub_skills[1].level_1_description}
        level={1}
        isChecked={lvl1}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[1].level_2_description}
        level={2}
        isChecked={lvl2}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[1].level_3_description}
        level={3}
        isChecked={lvl3}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[1].level_4_description}
        level={4}
        isChecked={lvl4}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[1].level_5_description}
        level={5}
        isChecked={lvl5}
        handleOnChange={handleOnChange}
      />

      <Card className="min-w-8 min-h-58 bg-[#ca005d] text-primary-foreground">
        <CardContent className={"font-extrabold text-l text-center pt-16"}>
          <div className="inline-block align-middle">
            {" "}
            {behavioural_sub_skills[2].sub_skill_name}
          </div>
        </CardContent>
      </Card>

      <MapperCard
        desc={behavioural_sub_skills[2].level_1_description}
        level={1}
        isChecked={lvl1}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[2].level_2_description}
        level={2}
        isChecked={lvl2}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[2].level_3_description}
        level={3}
        isChecked={lvl3}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[2].level_4_description}
        level={4}
        isChecked={lvl4}
        handleOnChange={handleOnChange}
      />
      <MapperCard
        desc={behavioural_sub_skills[2].level_5_description}
        level={5}
        isChecked={lvl5}
        handleOnChange={handleOnChange}
      />
    </>
  );
};

export default MapperBSkillRow;
