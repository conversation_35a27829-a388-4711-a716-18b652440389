"use client";

import { ChevronRight } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@core/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@core/components/ui/sidebar";

import { UserSquare, UserCog, Cog } from "lucide-react";
import Link from "next/link";

export function NavTech({ items, selected_item_tech }) {
  // const behavioural_skills = items.behavioural_skills;

  return (
    <SidebarGroup className={"pt-0"}>
      <SidebarMenu>
        <Collapsible
          key={1}
          asChild
          defaultOpen={
            selected_item_tech &&
            selected_item_tech?.id === selected_item_tech.id
              ? true
              : false
          }
          className="group/collapsible"
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip="Technical skills" className="pt-0">
                <Cog />
                <span>{"Technical skills"}</span>
                <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {items.map((item) => (
                  <SidebarMenuSubItem key={item.skill_name}>
                    <SidebarMenuSubButton asChild>
                      <Link href={"/technical/" + item.id}>
                        {selected_item_tech &&
                        selected_item_tech === item.skill_name ? (
                          <span className="text-primary font-bold">
                            {item.skill_name}
                          </span>
                        ) : (
                          <span>{item.skill_name}</span>
                        )}
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  );
}
