import { useState } from "react";

import {
  Calendar,
  Home,
  Inbox,
  Search,
  Settings,
  LayoutDashboard,
  Waypoints,
  SquareUser,
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  UserSquare,
  UserCog,
  Cog,
  User,
  Workflow,
  BookUser,
  SquareLibrary,
  GraduationCap,
  Store,
  MonitorCogIcon,
  FileCog,
} from "lucide-react";
import { useSidebar } from "@/components/ui/sidebar";

import { TeamSwitcher } from "@/components/team-switcher";
import { NavAdmin } from "@/components/nav-admin";
import { NavProjects } from "@/components/nav-projects";
import { NavRole } from "@/components/nav-role";
import { NavAdminDashboard } from "@/components/nav-admin-dashboard";
import { NavResources } from "@/components/nav-resources";
import { NavUser } from "@/components/nav-user";
import { But<PERSON> } from "@/components/ui/button";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
} from "@/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Menu items.
const items = [
  {
    title: "Skills Library",
    url: "#",
    icon: LayoutDashboard,
    isActive: true,
    items: [
      {
        title: "History",
        url: "#",
      },
      {
        title: "Starred",
        url: "#",
      },
      {
        title: "Settings",
        url: "#",
      },
    ],
  },
];

const data = {
  user: {
    name: "Richard Stephens",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },

  dashboard: [
    {
      name: "Manage skills",
      url: "/dashboard",
      icon: FileCog,
    },
  ],

  navAdmin: [
    {
      title: "Behavioural skills",
      url: "#",
      icon: UserSquare,
      isActive: false,
      items: [
        {
          title: "Strategic Thinking",
          url: "/strategic-thinking",
        },
        {
          title: "Purposeful Planning",
          url: "#",
        },
        {
          title: "Shaping Solutions",
          url: "#",
        },
        {
          title: "Customer Focus",
          url: "#",
        },
        {
          title: "Agile and Adaptable",
          url: "#",
        },
        {
          title: "Engage and Influence",
          url: "#",
        },
        {
          title: "Deliver Results",
          url: "#",
        },
        {
          title: "Collaborate Openly",
          url: "#",
        },
        {
          title: "Trust and Integrity",
          url: "#",
        },
        {
          title: "Develop Self",
          url: "#",
        },
        {
          title: "Enable Performance",
          url: "#",
        },
        {
          title: "Develop Others",
          url: "#",
        },
      ],
    },
  ],
  projects: [
    // {
    //   name: "Function",
    //   url: "#",
    //   icon: Workflow,
    // },
    // {
    //   name: "Owner",
    //   url: "#",
    //   icon: User,
    // },
  ],
  role: [
    {
      name: "Roles",
      url: "/roles",
      icon: BookUser,
    },
  ],
  resources: [
    {
      name: "Talent Marketplace",
      url: "/talent-marketplace",
      icon: Store,
    },
    // {
    //   name: "Interview bank",
    //   url: "#",
    //   icon: SquareLibrary,
    // },
    // {
    //   name: "Learning resources",
    //   url: "#",
    //   icon: GraduationCap,
    // },
  ],
};

const algorithm = [
  "Cyber Security Operations",
  "Security Architecture and Assurance",
];
const language = [
  "Security Compliance, Risk and Resilience",
  "Security, Demand, Capability and Awareness",
];

export function AdminSidebar(props) {
  const {
    state,
    open,
    setOpen,
    openMobile,
    setOpenMobile,
    isMobile,
    toggleSidebar,
  } = useSidebar();

  const [selected, setSelected] = useState("");

  const [showSkills, setShowSkills] = useState(false);
  const [typeSelected, setTypeSelected] = useState("");

  const [behaveSelected, setBehaveSelected] = useState(false);
  const [techSelected, setTechSelected] = useState(true);

  const changeSelectOptionHandler = (value) => {
    setSelected(value);
    setShowSkills(false);
  };

  const teamSelectOptionHandler = (value) => {
    setTeamSelected(value);
    setShowSkills(true);
  };

  const typeSelector = (value) => {
    props.handleShowSkills(value);
  };

  // const typeSelectOptionHandler = (event) => {
  //   setTypeSelected(event.target.value);
  // };

  // console.log("selected");
  // console.log(selected);

  // console.log("teamSelected");
  // console.log(teamSelected);

  /** Type variable to store different array for different dropdown */
  let type = null;

  /** This will be used to create set of options that user will see */
  let options = null;

  /** Setting Type variable according to dropdown */
  if (selected === "Security") {
    type = algorithm;
  } else if (selected === "Another Security") {
    type = language;
  }

  /** If "Type" is null or undefined then options will be null,
   * otherwise it will create a options iterable based on our array
   */
  if (type) {
    options = type.map((el) => (
      <SelectItem key={el} value={el}>
        {el}
      </SelectItem>
    ));
  }

  return (
    // <Sidebar collapsible="icon" {...props}>
    <Sidebar collapsible="offcanvas">
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavAdminDashboard projects={data.dashboard} />
        {/* <NavAdminDashboard /> */}
        {/* <NavAdmin items={data.navAdmin} /> */}
        <NavProjects projects={data.projects} />

        <div className="ml-6 pt-0">
          <Select>
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Security">Behavioural skills</SelectItem>
              <SelectItem value="Another Security">Technical skills</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="ml-6 pt-0">
          <Select>
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Head of Cyber Operations">
                Head of Cyber Operations
              </SelectItem>
              <SelectItem value="Head of Cyber Operations">
                Head of Cyber Operations
              </SelectItem>
              <SelectItem value="Senior Cyber Operations Analyst ">
                Senior Cyber Operations Analyst{" "}
              </SelectItem>
              <SelectItem value="Cyber Operations Analyst ">
                Cyber Operations Analyst{" "}
              </SelectItem>
              <SelectItem value="Lead Cyber Security Engineer">
                Lead Cyber Security Engineer
              </SelectItem>
              <SelectItem value="Cyber Security Engineer">
                Cyber Security Engineer
              </SelectItem>
              <SelectItem value="Security Business Partner">
                Security Business Partner
              </SelectItem>
              <SelectItem value="Senior PKI Manager">
                Senior PKI Manager
              </SelectItem>
              <SelectItem value="SMKI RA Manager">SMKI RA Manager</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {behaveSelected && (
          <div className="ml-6 pt-0">
            <Select>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Select a skill" />
              </SelectTrigger>
              {/* <SelectContent>{options}</SelectContent> */}
              <SelectContent>
                <SelectItem value="Thinks Strategically">
                  Thinks Strategically
                </SelectItem>
                <SelectItem value="Plans with Purpose ">
                  Plans with Purpose{" "}
                </SelectItem>
                <SelectItem value="Shapes Solutions">
                  Shapes Solutions
                </SelectItem>
                <SelectItem value="Customer Focused">
                  Customer Focused
                </SelectItem>
                <SelectItem value="Adapts">Adapts</SelectItem>
                <SelectItem value="Influences Stakeholders">
                  Influences Stakeholders
                </SelectItem>
                <SelectItem value="Delivers Results ">
                  Delivers Results{" "}
                </SelectItem>
                <SelectItem value="Collaborates Openly">
                  Collaborates Openly
                </SelectItem>
                <SelectItem value="Acts with Integrity ">
                  Acts with Integrity{" "}
                </SelectItem>
                <SelectItem value="Develops Self">Develops Self</SelectItem>
                <SelectItem value="Enables Performance">
                  Enables Performance
                </SelectItem>
                <SelectItem value="Develops Others">Develops Others</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        {techSelected && (
          <div className="ml-6 pt-0">
            <Select>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Select a skill" />
              </SelectTrigger>
              {/* <SelectContent>{options}</SelectContent> */}
              <SelectContent>
                <SelectItem value="BCDR">BCDR</SelectItem>
                <SelectItem value="Change Management">
                  Change Management
                </SelectItem>
                <SelectItem value="Certification Management">
                  Certification Management
                </SelectItem>
                <SelectItem value="Compliance and Regulatory Assurance">
                  Compliance and Regulatory Assurance
                </SelectItem>
                <SelectItem value="Data Analytics and Insights">
                  Data Analytics and Insights
                </SelectItem>
                <SelectItem value="Development Lifecyclex">
                  Development Lifecycle
                </SelectItem>
                <SelectItem value="Incident Response Lifecycle">
                  Incident Response Lifecycle
                </SelectItem>
                <SelectItem value="Infrastructure and Cloud Computing">
                  Infrastructure and Cloud Computing
                </SelectItem>
                <SelectItem value="Procurement">Procurement</SelectItem>
                <SelectItem value="Risk Management">Risk Management</SelectItem>
                <SelectItem value="Supplier Management">
                  Supplier Management
                </SelectItem>
                <SelectItem value="Threat Intelligence">
                  Threat Intelligence
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="p-3">
          <div>
            <h1 className="text-sm font-extrabold tracking-tight text-balance text-dccblue pb-2">
              Skills added:
            </h1>
          </div>
          {/* bg-slate-800  */}
          <div
            id="chip"
            class=" mb-2 w-28 relative rounded-md flex bg-dccgreen py-0.5 pl-2.5 pr-8 border border-transparent text-sm text-white transition-all shadow-sm font-semibold"
          >
            BCDR
            <button
              class="flex items-center justify-center transition-all p-1 rounded-md text-white hover:bg-white/10 active:bg-white/10 absolute top-0.5 right-0.5"
              type="button"
              onclick="closeAlert()"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 16 16"
                fill="currentColor"
                class="w-4 h-4"
              >
                <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
              </svg>
            </button>
          </div>
        </div>
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
