"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./pages/dashboard.js":
/*!****************************!*\
  !*** ./pages/dashboard.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ console.log(\"tech_data_user\");\n    console.log(tech_data_user);\n    console.log(\"userData\");\n    console.log(userData.role);\n    const filterTest = tech_data_user.filter((item)=>item.role_mapping.role_id === userData.role);\n    console.log(\"filterTest\");\n    console.log(filterTest);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // filter the array based on user role\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                href: \"#\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                            className: \"hidden md:block\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                children: \"My Skills Snapshot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"KNaG7HRFKj3fWN5peDVgbtbg7mo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/dashboard.js\n"));

/***/ })

});