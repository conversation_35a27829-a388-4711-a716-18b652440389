"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-black: #000;\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --tracking-tight: -0.025em;\\n    --tracking-widest: 0.1em;\\n    --leading-tight: 1.25;\\n    --radius-xs: 0.125rem;\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-geist-sans);\\n    --default-mono-font-family: var(--font-geist-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .\\\\@container\\\\/card-header {\\n    container-type: inline-size;\\n    container-name: card-header;\\n  }\\n  .pointer-events-none {\\n    pointer-events: none;\\n  }\\n  .invisible {\\n    visibility: hidden;\\n  }\\n  .sr-only {\\n    position: absolute;\\n    width: 1px;\\n    height: 1px;\\n    padding: 0;\\n    margin: -1px;\\n    overflow: hidden;\\n    clip: rect(0, 0, 0, 0);\\n    white-space: nowrap;\\n    border-width: 0;\\n  }\\n  .absolute {\\n    position: absolute;\\n  }\\n  .fixed {\\n    position: fixed;\\n  }\\n  .relative {\\n    position: relative;\\n  }\\n  .inset-0 {\\n    inset: calc(var(--spacing) * 0);\\n  }\\n  .inset-x-0 {\\n    inset-inline: calc(var(--spacing) * 0);\\n  }\\n  .inset-y-0 {\\n    inset-block: calc(var(--spacing) * 0);\\n  }\\n  .-top-12 {\\n    top: calc(var(--spacing) * -12);\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .top-0\\\\.5 {\\n    top: calc(var(--spacing) * 0.5);\\n  }\\n  .top-1\\\\.5 {\\n    top: calc(var(--spacing) * 1.5);\\n  }\\n  .top-1\\\\/2 {\\n    top: calc(1/2 * 100%);\\n  }\\n  .top-2 {\\n    top: calc(var(--spacing) * 2);\\n  }\\n  .top-3\\\\.5 {\\n    top: calc(var(--spacing) * 3.5);\\n  }\\n  .top-4 {\\n    top: calc(var(--spacing) * 4);\\n  }\\n  .top-\\\\[50\\\\%\\\\] {\\n    top: 50%;\\n  }\\n  .-right-12 {\\n    right: calc(var(--spacing) * -12);\\n  }\\n  .right-0 {\\n    right: calc(var(--spacing) * 0);\\n  }\\n  .right-0\\\\.5 {\\n    right: calc(var(--spacing) * 0.5);\\n  }\\n  .right-1 {\\n    right: calc(var(--spacing) * 1);\\n  }\\n  .right-2 {\\n    right: calc(var(--spacing) * 2);\\n  }\\n  .right-3 {\\n    right: calc(var(--spacing) * 3);\\n  }\\n  .right-4 {\\n    right: calc(var(--spacing) * 4);\\n  }\\n  .-bottom-12 {\\n    bottom: calc(var(--spacing) * -12);\\n  }\\n  .bottom-0 {\\n    bottom: calc(var(--spacing) * 0);\\n  }\\n  .-left-12 {\\n    left: calc(var(--spacing) * -12);\\n  }\\n  .left-0 {\\n    left: calc(var(--spacing) * 0);\\n  }\\n  .left-1\\\\/2 {\\n    left: calc(1/2 * 100%);\\n  }\\n  .left-2 {\\n    left: calc(var(--spacing) * 2);\\n  }\\n  .left-\\\\[50\\\\%\\\\] {\\n    left: 50%;\\n  }\\n  .z-10 {\\n    z-index: 10;\\n  }\\n  .z-20 {\\n    z-index: 20;\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .col-start-2 {\\n    grid-column-start: 2;\\n  }\\n  .row-span-2 {\\n    grid-row: span 2 / span 2;\\n  }\\n  .row-start-1 {\\n    grid-row-start: 1;\\n  }\\n  .m-6 {\\n    margin: calc(var(--spacing) * 6);\\n  }\\n  .m-auto {\\n    margin: auto;\\n  }\\n  .-mx-1 {\\n    margin-inline: calc(var(--spacing) * -1);\\n  }\\n  .mx-2 {\\n    margin-inline: calc(var(--spacing) * 2);\\n  }\\n  .mx-3\\\\.5 {\\n    margin-inline: calc(var(--spacing) * 3.5);\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .my-1 {\\n    margin-block: calc(var(--spacing) * 1);\\n  }\\n  .-mt-4 {\\n    margin-top: calc(var(--spacing) * -4);\\n  }\\n  .mt-0 {\\n    margin-top: calc(var(--spacing) * 0);\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mt-4 {\\n    margin-top: calc(var(--spacing) * 4);\\n  }\\n  .mt-6 {\\n    margin-top: calc(var(--spacing) * 6);\\n  }\\n  .mt-8 {\\n    margin-top: calc(var(--spacing) * 8);\\n  }\\n  .mt-10 {\\n    margin-top: calc(var(--spacing) * 10);\\n  }\\n  .mt-auto {\\n    margin-top: auto;\\n  }\\n  .mb-1 {\\n    margin-bottom: calc(var(--spacing) * 1);\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-10 {\\n    margin-bottom: calc(var(--spacing) * 10);\\n  }\\n  .-ml-4 {\\n    margin-left: calc(var(--spacing) * -4);\\n  }\\n  .ml-0 {\\n    margin-left: calc(var(--spacing) * 0);\\n  }\\n  .ml-1 {\\n    margin-left: calc(var(--spacing) * 1);\\n  }\\n  .ml-3 {\\n    margin-left: calc(var(--spacing) * 3);\\n  }\\n  .ml-6 {\\n    margin-left: calc(var(--spacing) * 6);\\n  }\\n  .ml-auto {\\n    margin-left: auto;\\n  }\\n  .block {\\n    display: block;\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .hidden {\\n    display: none;\\n  }\\n  .inline-block {\\n    display: inline-block;\\n  }\\n  .inline-flex {\\n    display: inline-flex;\\n  }\\n  .table {\\n    display: table;\\n  }\\n  .table-caption {\\n    display: table-caption;\\n  }\\n  .table-cell {\\n    display: table-cell;\\n  }\\n  .table-row {\\n    display: table-row;\\n  }\\n  .aspect-square {\\n    aspect-ratio: 1 / 1;\\n  }\\n  .size-2 {\\n    width: calc(var(--spacing) * 2);\\n    height: calc(var(--spacing) * 2);\\n  }\\n  .size-2\\\\.5 {\\n    width: calc(var(--spacing) * 2.5);\\n    height: calc(var(--spacing) * 2.5);\\n  }\\n  .size-3\\\\.5 {\\n    width: calc(var(--spacing) * 3.5);\\n    height: calc(var(--spacing) * 3.5);\\n  }\\n  .size-4 {\\n    width: calc(var(--spacing) * 4);\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .size-6 {\\n    width: calc(var(--spacing) * 6);\\n    height: calc(var(--spacing) * 6);\\n  }\\n  .size-7 {\\n    width: calc(var(--spacing) * 7);\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .size-8 {\\n    width: calc(var(--spacing) * 8);\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .size-9 {\\n    width: calc(var(--spacing) * 9);\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .size-full {\\n    width: 100%;\\n    height: 100%;\\n  }\\n  .h-4 {\\n    height: calc(var(--spacing) * 4);\\n  }\\n  .h-5 {\\n    height: calc(var(--spacing) * 5);\\n  }\\n  .h-7 {\\n    height: calc(var(--spacing) * 7);\\n  }\\n  .h-8 {\\n    height: calc(var(--spacing) * 8);\\n  }\\n  .h-9 {\\n    height: calc(var(--spacing) * 9);\\n  }\\n  .h-10 {\\n    height: calc(var(--spacing) * 10);\\n  }\\n  .h-12 {\\n    height: calc(var(--spacing) * 12);\\n  }\\n  .h-16 {\\n    height: calc(var(--spacing) * 16);\\n  }\\n  .h-\\\\[calc\\\\(100\\\\%-1px\\\\)\\\\] {\\n    height: calc(100% - 1px);\\n  }\\n  .h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n    height: var(--radix-select-trigger-height);\\n  }\\n  .h-auto {\\n    height: auto;\\n  }\\n  .h-full {\\n    height: 100%;\\n  }\\n  .h-px {\\n    height: 1px;\\n  }\\n  .h-screen {\\n    height: 100vh;\\n  }\\n  .h-svh {\\n    height: 100svh;\\n  }\\n  .max-h-\\\\(--radix-dropdown-menu-content-available-height\\\\) {\\n    max-height: var(--radix-dropdown-menu-content-available-height);\\n  }\\n  .max-h-\\\\(--radix-select-content-available-height\\\\) {\\n    max-height: var(--radix-select-content-available-height);\\n  }\\n  .max-h-\\\\[300px\\\\] {\\n    max-height: 300px;\\n  }\\n  .min-h-0 {\\n    min-height: calc(var(--spacing) * 0);\\n  }\\n  .min-h-58 {\\n    min-height: calc(var(--spacing) * 58);\\n  }\\n  .min-h-svh {\\n    min-height: 100svh;\\n  }\\n  .w-\\\\(--sidebar-width\\\\) {\\n    width: var(--sidebar-width);\\n  }\\n  .w-3\\\\/4 {\\n    width: calc(3/4 * 100%);\\n  }\\n  .w-4 {\\n    width: calc(var(--spacing) * 4);\\n  }\\n  .w-5 {\\n    width: calc(var(--spacing) * 5);\\n  }\\n  .w-8 {\\n    width: calc(var(--spacing) * 8);\\n  }\\n  .w-28 {\\n    width: calc(var(--spacing) * 28);\\n  }\\n  .w-48 {\\n    width: calc(var(--spacing) * 48);\\n  }\\n  .w-64 {\\n    width: calc(var(--spacing) * 64);\\n  }\\n  .w-72 {\\n    width: calc(var(--spacing) * 72);\\n  }\\n  .w-\\\\[--radix-dropdown-menu-trigger-width\\\\] {\\n    width: --radix-dropdown-menu-trigger-width;\\n  }\\n  .w-\\\\[150px\\\\] {\\n    width: 150px;\\n  }\\n  .w-\\\\[200px\\\\] {\\n    width: 200px;\\n  }\\n  .w-\\\\[220px\\\\] {\\n    width: 220px;\\n  }\\n  .w-\\\\[300px\\\\] {\\n    width: 300px;\\n  }\\n  .w-\\\\[400px\\\\] {\\n    width: 400px;\\n  }\\n  .w-auto {\\n    width: auto;\\n  }\\n  .w-fit {\\n    width: fit-content;\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-\\\\(--skeleton-width\\\\) {\\n    max-width: var(--skeleton-width);\\n  }\\n  .max-w-\\\\[calc\\\\(100\\\\%-2rem\\\\)\\\\] {\\n    max-width: calc(100% - 2rem);\\n  }\\n  .max-w-sm {\\n    max-width: var(--container-sm);\\n  }\\n  .min-w-0 {\\n    min-width: calc(var(--spacing) * 0);\\n  }\\n  .min-w-5 {\\n    min-width: calc(var(--spacing) * 5);\\n  }\\n  .min-w-8 {\\n    min-width: calc(var(--spacing) * 8);\\n  }\\n  .min-w-56 {\\n    min-width: calc(var(--spacing) * 56);\\n  }\\n  .min-w-\\\\[8rem\\\\] {\\n    min-width: 8rem;\\n  }\\n  .min-w-\\\\[12rem\\\\] {\\n    min-width: 12rem;\\n  }\\n  .min-w-\\\\[20px\\\\] {\\n    min-width: 20px;\\n  }\\n  .min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n    min-width: var(--radix-select-trigger-width);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .shrink-0 {\\n    flex-shrink: 0;\\n  }\\n  .grow-0 {\\n    flex-grow: 0;\\n  }\\n  .basis-full {\\n    flex-basis: 100%;\\n  }\\n  .caption-bottom {\\n    caption-side: bottom;\\n  }\\n  .origin-\\\\(--radix-dropdown-menu-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-hover-card-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-hover-card-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-menubar-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-menubar-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-popover-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-popover-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-select-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-select-content-transform-origin);\\n  }\\n  .origin-\\\\(--radix-tooltip-content-transform-origin\\\\) {\\n    transform-origin: var(--radix-tooltip-content-transform-origin);\\n  }\\n  .-translate-x-1\\\\/2 {\\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-x-px {\\n    --tw-translate-x: -1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-x-\\\\[-50\\\\%\\\\] {\\n    --tw-translate-x: -50%;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-x-px {\\n    --tw-translate-x: 1px;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .-translate-y-1\\\\/2 {\\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-0\\\\.5 {\\n    --tw-translate-y: calc(var(--spacing) * 0.5);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-\\\\[-50\\\\%\\\\] {\\n    --tw-translate-y: -50%;\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .translate-y-\\\\[calc\\\\(-50\\\\%_-_2px\\\\)\\\\] {\\n    --tw-translate-y: calc(-50% - 2px);\\n    translate: var(--tw-translate-x) var(--tw-translate-y);\\n  }\\n  .rotate-45 {\\n    rotate: 45deg;\\n  }\\n  .rotate-90 {\\n    rotate: 90deg;\\n  }\\n  .transform {\\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\\n  }\\n  .animate-in {\\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n  }\\n  .animate-pulse {\\n    animation: var(--animate-pulse);\\n  }\\n  .cursor-default {\\n    cursor: default;\\n  }\\n  .scroll-my-1 {\\n    scroll-margin-block: calc(var(--spacing) * 1);\\n  }\\n  .scroll-py-1 {\\n    scroll-padding-block: calc(var(--spacing) * 1);\\n  }\\n  .appearance-none {\\n    appearance: none;\\n  }\\n  .auto-rows-min {\\n    grid-auto-rows: min-content;\\n  }\\n  .grid-cols-\\\\[10\\\\%_18\\\\%_18\\\\%_18\\\\%_18\\\\%_18\\\\%\\\\] {\\n    grid-template-columns: 10% 18% 18% 18% 18% 18%;\\n  }\\n  .grid-cols-\\\\[20\\\\%_20\\\\%_20\\\\%_20\\\\%_20\\\\%\\\\] {\\n    grid-template-columns: 20% 20% 20% 20% 20%;\\n  }\\n  .grid-rows-\\\\[auto_auto\\\\] {\\n    grid-template-rows: auto auto;\\n  }\\n  .flex-col {\\n    flex-direction: column;\\n  }\\n  .flex-col-reverse {\\n    flex-direction: column-reverse;\\n  }\\n  .flex-row {\\n    flex-direction: row;\\n  }\\n  .flex-wrap {\\n    flex-wrap: wrap;\\n  }\\n  .place-items-center {\\n    place-items: center;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-0 {\\n    gap: calc(var(--spacing) * 0);\\n  }\\n  .gap-1 {\\n    gap: calc(var(--spacing) * 1);\\n  }\\n  .gap-1\\\\.5 {\\n    gap: calc(var(--spacing) * 1.5);\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-3 {\\n    gap: calc(var(--spacing) * 3);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .space-x-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-x-reverse: 0;\\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\\n    }\\n  }\\n  .self-start {\\n    align-self: flex-start;\\n  }\\n  .justify-self-end {\\n    justify-self: flex-end;\\n  }\\n  .truncate {\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    white-space: nowrap;\\n  }\\n  .overflow-auto {\\n    overflow: auto;\\n  }\\n  .overflow-hidden {\\n    overflow: hidden;\\n  }\\n  .overflow-x-auto {\\n    overflow-x: auto;\\n  }\\n  .overflow-x-hidden {\\n    overflow-x: hidden;\\n  }\\n  .overflow-y-auto {\\n    overflow-y: auto;\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-\\\\[2px\\\\] {\\n    border-radius: 2px;\\n  }\\n  .rounded-\\\\[4px\\\\] {\\n    border-radius: 4px;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n  .rounded-md {\\n    border-radius: calc(var(--radius) - 2px);\\n  }\\n  .rounded-sm {\\n    border-radius: calc(var(--radius) - 4px);\\n  }\\n  .rounded-xl {\\n    border-radius: calc(var(--radius) + 4px);\\n  }\\n  .rounded-xs {\\n    border-radius: var(--radius-xs);\\n  }\\n  .rounded-l-full {\\n    border-top-left-radius: calc(infinity * 1px);\\n    border-bottom-left-radius: calc(infinity * 1px);\\n  }\\n  .rounded-tl-xl {\\n    border-top-left-radius: calc(var(--radius) + 4px);\\n  }\\n  .rounded-tr-xl {\\n    border-top-right-radius: calc(var(--radius) + 4px);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-t {\\n    border-top-style: var(--tw-border-style);\\n    border-top-width: 1px;\\n  }\\n  .border-r {\\n    border-right-style: var(--tw-border-style);\\n    border-right-width: 1px;\\n  }\\n  .border-b {\\n    border-bottom-style: var(--tw-border-style);\\n    border-bottom-width: 1px;\\n  }\\n  .border-l {\\n    border-left-style: var(--tw-border-style);\\n    border-left-width: 1px;\\n  }\\n  .border-gray-300 {\\n    border-color: var(--color-gray-300);\\n  }\\n  .border-gray-400 {\\n    border-color: var(--color-gray-400);\\n  }\\n  .border-input {\\n    border-color: var(--input);\\n  }\\n  .border-sidebar-border {\\n    border-color: var(--sidebar-border);\\n  }\\n  .border-slate-200 {\\n    border-color: var(--color-slate-200);\\n  }\\n  .border-transparent {\\n    border-color: transparent;\\n  }\\n  .bg-\\\\[\\\\#1f144a\\\\] {\\n    background-color: #1f144a;\\n  }\\n  .bg-\\\\[\\\\#5c2071\\\\] {\\n    background-color: #5c2071;\\n  }\\n  .bg-\\\\[\\\\#009cbb\\\\] {\\n    background-color: #009cbb;\\n  }\\n  .bg-\\\\[\\\\#9ca299\\\\] {\\n    background-color: #9ca299;\\n  }\\n  .bg-\\\\[\\\\#95c11f\\\\] {\\n    background-color: #95c11f;\\n  }\\n  .bg-\\\\[\\\\#505253\\\\] {\\n    background-color: #505253;\\n  }\\n  .bg-\\\\[\\\\#ca005d\\\\] {\\n    background-color: #ca005d;\\n  }\\n  .bg-accent {\\n    background-color: var(--accent);\\n  }\\n  .bg-background {\\n    background-color: var(--background);\\n  }\\n  .bg-black\\\\/50 {\\n    background-color: color-mix(in srgb, #000 50%, transparent);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\\n    }\\n  }\\n  .bg-border {\\n    background-color: var(--border);\\n  }\\n  .bg-card {\\n    background-color: var(--card);\\n  }\\n  .bg-dccdarkgrey {\\n    background-color: var(--dccdarkgrey);\\n  }\\n  .bg-dccgreen {\\n    background-color: var(--dccgreen);\\n  }\\n  .bg-dccorange {\\n    background-color: var(--dccorange);\\n  }\\n  .bg-destructive {\\n    background-color: var(--destructive);\\n  }\\n  .bg-green-600 {\\n    background-color: var(--color-green-600);\\n  }\\n  .bg-muted {\\n    background-color: var(--muted);\\n  }\\n  .bg-muted\\\\/50 {\\n    background-color: var(--muted);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n    }\\n  }\\n  .bg-popover {\\n    background-color: var(--popover);\\n  }\\n  .bg-primary {\\n    background-color: var(--primary);\\n  }\\n  .bg-primary\\\\/20 {\\n    background-color: var(--primary);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\\n    }\\n  }\\n  .bg-secondary {\\n    background-color: var(--secondary);\\n  }\\n  .bg-sidebar {\\n    background-color: var(--sidebar);\\n  }\\n  .bg-sidebar-border {\\n    background-color: var(--sidebar-border);\\n  }\\n  .bg-sidebar-primary {\\n    background-color: var(--sidebar-primary);\\n  }\\n  .bg-slate-800 {\\n    background-color: var(--color-slate-800);\\n  }\\n  .bg-transparent {\\n    background-color: transparent;\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .fill-current {\\n    fill: currentcolor;\\n  }\\n  .fill-primary {\\n    fill: var(--primary);\\n  }\\n  .p-0 {\\n    padding: calc(var(--spacing) * 0);\\n  }\\n  .p-1 {\\n    padding: calc(var(--spacing) * 1);\\n  }\\n  .p-1\\\\.5 {\\n    padding: calc(var(--spacing) * 1.5);\\n  }\\n  .p-2 {\\n    padding: calc(var(--spacing) * 2);\\n  }\\n  .p-3 {\\n    padding: calc(var(--spacing) * 3);\\n  }\\n  .p-4 {\\n    padding: calc(var(--spacing) * 4);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .p-\\\\[3px\\\\] {\\n    padding: 3px;\\n  }\\n  .px-1 {\\n    padding-inline: calc(var(--spacing) * 1);\\n  }\\n  .px-2 {\\n    padding-inline: calc(var(--spacing) * 2);\\n  }\\n  .px-2\\\\.5 {\\n    padding-inline: calc(var(--spacing) * 2.5);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-6 {\\n    padding-inline: calc(var(--spacing) * 6);\\n  }\\n  .py-0\\\\.5 {\\n    padding-block: calc(var(--spacing) * 0.5);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-1\\\\.5 {\\n    padding-block: calc(var(--spacing) * 1.5);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-4 {\\n    padding-block: calc(var(--spacing) * 4);\\n  }\\n  .py-6 {\\n    padding-block: calc(var(--spacing) * 6);\\n  }\\n  .pt-0 {\\n    padding-top: calc(var(--spacing) * 0);\\n  }\\n  .pt-1 {\\n    padding-top: calc(var(--spacing) * 1);\\n  }\\n  .pt-3 {\\n    padding-top: calc(var(--spacing) * 3);\\n  }\\n  .pt-4 {\\n    padding-top: calc(var(--spacing) * 4);\\n  }\\n  .pt-6 {\\n    padding-top: calc(var(--spacing) * 6);\\n  }\\n  .pt-16 {\\n    padding-top: calc(var(--spacing) * 16);\\n  }\\n  .pr-2 {\\n    padding-right: calc(var(--spacing) * 2);\\n  }\\n  .pr-8 {\\n    padding-right: calc(var(--spacing) * 8);\\n  }\\n  .pb-0 {\\n    padding-bottom: calc(var(--spacing) * 0);\\n  }\\n  .pb-1 {\\n    padding-bottom: calc(var(--spacing) * 1);\\n  }\\n  .pb-2 {\\n    padding-bottom: calc(var(--spacing) * 2);\\n  }\\n  .pb-4 {\\n    padding-bottom: calc(var(--spacing) * 4);\\n  }\\n  .pl-2 {\\n    padding-left: calc(var(--spacing) * 2);\\n  }\\n  .pl-2\\\\.5 {\\n    padding-left: calc(var(--spacing) * 2.5);\\n  }\\n  .pl-3 {\\n    padding-left: calc(var(--spacing) * 3);\\n  }\\n  .pl-4 {\\n    padding-left: calc(var(--spacing) * 4);\\n  }\\n  .pl-8 {\\n    padding-left: calc(var(--spacing) * 8);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-left {\\n    text-align: left;\\n  }\\n  .text-right {\\n    text-align: right;\\n  }\\n  .align-middle {\\n    vertical-align: middle;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-base {\\n    font-size: var(--text-base);\\n    line-height: var(--tw-leading, var(--text-base--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xs {\\n    font-size: var(--text-xs);\\n    line-height: var(--tw-leading, var(--text-xs--line-height));\\n  }\\n  .leading-none {\\n    --tw-leading: 1;\\n    line-height: 1;\\n  }\\n  .leading-tight {\\n    --tw-leading: var(--leading-tight);\\n    line-height: var(--leading-tight);\\n  }\\n  .font-bold {\\n    --tw-font-weight: var(--font-weight-bold);\\n    font-weight: var(--font-weight-bold);\\n  }\\n  .font-extrabold {\\n    --tw-font-weight: var(--font-weight-extrabold);\\n    font-weight: var(--font-weight-extrabold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-normal {\\n    --tw-font-weight: var(--font-weight-normal);\\n    font-weight: var(--font-weight-normal);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .tracking-tight {\\n    --tw-tracking: var(--tracking-tight);\\n    letter-spacing: var(--tracking-tight);\\n  }\\n  .tracking-widest {\\n    --tw-tracking: var(--tracking-widest);\\n    letter-spacing: var(--tracking-widest);\\n  }\\n  .text-balance {\\n    text-wrap: balance;\\n  }\\n  .break-words {\\n    overflow-wrap: break-word;\\n  }\\n  .whitespace-nowrap {\\n    white-space: nowrap;\\n  }\\n  .\\\\!text-green-600 {\\n    color: var(--color-green-600) !important;\\n  }\\n  .\\\\!text-red-600 {\\n    color: var(--color-red-600) !important;\\n  }\\n  .text-black {\\n    color: var(--color-black);\\n  }\\n  .text-card-foreground {\\n    color: var(--card-foreground);\\n  }\\n  .text-current {\\n    color: currentcolor;\\n  }\\n  .text-dccblue {\\n    color: var(--dccblue);\\n  }\\n  .text-foreground {\\n    color: var(--foreground);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-900 {\\n    color: var(--color-gray-900);\\n  }\\n  .text-muted-foreground {\\n    color: var(--muted-foreground);\\n  }\\n  .text-popover-foreground {\\n    color: var(--popover-foreground);\\n  }\\n  .text-primary {\\n    color: var(--primary);\\n  }\\n  .text-primary-foreground {\\n    color: var(--primary-foreground);\\n  }\\n  .text-secondary-foreground {\\n    color: var(--secondary-foreground);\\n  }\\n  .text-sidebar-foreground {\\n    color: var(--sidebar-foreground);\\n  }\\n  .text-sidebar-foreground\\\\/70 {\\n    color: var(--sidebar-foreground);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\\n    }\\n  }\\n  .text-sidebar-primary-foreground {\\n    color: var(--sidebar-primary-foreground);\\n  }\\n  .text-slate-700 {\\n    color: var(--color-slate-700);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .tabular-nums {\\n    --tw-numeric-spacing: tabular-nums;\\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\\n  }\\n  .underline-offset-4 {\\n    text-underline-offset: 4px;\\n  }\\n  .antialiased {\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n  .placeholder-gray-400 {\\n    &::placeholder {\\n      color: var(--color-gray-400);\\n    }\\n  }\\n  .opacity-0 {\\n    opacity: 0%;\\n  }\\n  .opacity-50 {\\n    opacity: 50%;\\n  }\\n  .opacity-70 {\\n    opacity: 70%;\\n  }\\n  .opacity-100 {\\n    opacity: 100%;\\n  }\\n  .shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-border\\\\)\\\\)\\\\] {\\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-lg {\\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-none {\\n    --tw-shadow: 0 0 #0000;\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-sm {\\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .shadow-xs {\\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .ring-sidebar-ring {\\n    --tw-ring-color: var(--sidebar-ring);\\n  }\\n  .ring-offset-background {\\n    --tw-ring-offset-color: var(--background);\\n  }\\n  .outline-hidden {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n    @media (forced-colors: active) {\\n      outline: 2px solid transparent;\\n      outline-offset: 2px;\\n    }\\n  }\\n  .outline {\\n    outline-style: var(--tw-outline-style);\\n    outline-width: 1px;\\n  }\\n  .filter {\\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\\n  }\\n  .transition {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[color\\\\,box-shadow\\\\] {\\n    transition-property: color,box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[left\\\\,right\\\\,width\\\\] {\\n    transition-property: left,right,width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[margin\\\\,opacity\\\\] {\\n    transition-property: margin,opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\,height\\\\,padding\\\\] {\\n    transition-property: width,height,padding;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-\\\\[width\\\\] {\\n    transition-property: width;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-all {\\n    transition-property: all;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-opacity {\\n    transition-property: opacity;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-shadow {\\n    transition-property: box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-transform {\\n    transition-property: transform, translate, scale, rotate;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-none {\\n    transition-property: none;\\n  }\\n  .duration-200 {\\n    --tw-duration: 200ms;\\n    transition-duration: 200ms;\\n  }\\n  .duration-300 {\\n    --tw-duration: 300ms;\\n    transition-duration: 300ms;\\n  }\\n  .ease-in-out {\\n    --tw-ease: var(--ease-in-out);\\n    transition-timing-function: var(--ease-in-out);\\n  }\\n  .ease-linear {\\n    --tw-ease: linear;\\n    transition-timing-function: linear;\\n  }\\n  .fade-in-0 {\\n    --tw-enter-opacity: calc(0/100);\\n    --tw-enter-opacity: 0;\\n  }\\n  .outline-none {\\n    --tw-outline-style: none;\\n    outline-style: none;\\n  }\\n  .select-none {\\n    -webkit-user-select: none;\\n    user-select: none;\\n  }\\n  .zoom-in-95 {\\n    --tw-enter-scale: calc(95*1%);\\n    --tw-enter-scale: .95;\\n  }\\n  .group-focus-within\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):focus-within *) {\\n      opacity: 100%;\\n    }\\n  }\\n  .group-hover\\\\/menu-item\\\\:opacity-100 {\\n    &:is(:where(.group\\\\/menu-item):hover *) {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .group-has-data-\\\\[sidebar\\\\=menu-action\\\\]\\\\/menu-item\\\\:pr-8 {\\n    &:is(:where(.group\\\\/menu-item):has(*[data-sidebar=\\\"menu-action\\\"]) *) {\\n      padding-right: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:-mt-8 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      margin-top: calc(var(--spacing) * -8);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      display: none;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:size-8\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--spacing) * 8) !important;\\n      height: calc(var(--spacing) * 8) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\(--sidebar-width-icon\\\\) {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: var(--sidebar-width-icon);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:w-\\\\[calc\\\\(var\\\\(--sidebar-width-icon\\\\)\\\\+\\\\(--spacing\\\\(4\\\\)\\\\)\\\\+2px\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:overflow-hidden {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      overflow: hidden;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-0\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 0) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:p-2\\\\! {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      padding: calc(var(--spacing) * 2) !important;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=icon\\\\]\\\\:opacity-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"icon\\\"] *) {\\n      opacity: 0%;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:right-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      right: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:left-\\\\[calc\\\\(var\\\\(--sidebar-width\\\\)\\\\*-1\\\\)\\\\] {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      left: calc(var(--sidebar-width) * -1);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:w-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      width: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:translate-x-0 {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      --tw-translate-x: calc(var(--spacing) * 0);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .group-data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none {\\n    &:is(:where(.group)[data-disabled=\\\"true\\\"] *) {\\n      pointer-events: none;\\n    }\\n  }\\n  .group-data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50 {\\n    &:is(:where(.group)[data-disabled=\\\"true\\\"] *) {\\n      opacity: 50%;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:-right-4 {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      right: calc(var(--spacing) * -4);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=left\\\\]\\\\:border-r {\\n    &:is(:where(.group)[data-side=\\\"left\\\"] *) {\\n      border-right-style: var(--tw-border-style);\\n      border-right-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:left-0 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      left: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:rotate-180 {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      rotate: 180deg;\\n    }\\n  }\\n  .group-data-\\\\[side\\\\=right\\\\]\\\\:border-l {\\n    &:is(:where(.group)[data-side=\\\"right\\\"] *) {\\n      border-left-style: var(--tw-border-style);\\n      border-left-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[state\\\\=open\\\\]\\\\/collapsible\\\\:rotate-90 {\\n    &:is(:where(.group\\\\/collapsible)[data-state=\\\"open\\\"] *) {\\n      rotate: 90deg;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:rounded-lg {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-radius: var(--radius);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-style: var(--tw-border-style);\\n      border-width: 1px;\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:border-sidebar-border {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      border-color: var(--sidebar-border);\\n    }\\n  }\\n  .group-data-\\\\[variant\\\\=floating\\\\]\\\\:shadow-sm {\\n    &:is(:where(.group)[data-variant=\\\"floating\\\"] *) {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .peer-hover\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button):hover ~ *) {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .peer-disabled\\\\:cursor-not-allowed {\\n    &:is(:where(.peer):disabled ~ *) {\\n      cursor: not-allowed;\\n    }\\n  }\\n  .peer-disabled\\\\:opacity-50 {\\n    &:is(:where(.peer):disabled ~ *) {\\n      opacity: 50%;\\n    }\\n  }\\n  .peer-data-\\\\[active\\\\=true\\\\]\\\\/menu-button\\\\:text-sidebar-accent-foreground {\\n    &:is(:where(.peer\\\\/menu-button)[data-active=\\\"true\\\"] ~ *) {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=default\\\\]\\\\/menu-button\\\\:top-1\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"default\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=lg\\\\]\\\\/menu-button\\\\:top-2\\\\.5 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"lg\\\"] ~ *) {\\n      top: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .peer-data-\\\\[size\\\\=sm\\\\]\\\\/menu-button\\\\:top-1 {\\n    &:is(:where(.peer\\\\/menu-button)[data-size=\\\"sm\\\"] ~ *) {\\n      top: calc(var(--spacing) * 1);\\n    }\\n  }\\n  .selection\\\\:bg-primary {\\n    & *::selection {\\n      background-color: var(--primary);\\n    }\\n    &::selection {\\n      background-color: var(--primary);\\n    }\\n  }\\n  .selection\\\\:text-primary-foreground {\\n    & *::selection {\\n      color: var(--primary-foreground);\\n    }\\n    &::selection {\\n      color: var(--primary-foreground);\\n    }\\n  }\\n  .file\\\\:inline-flex {\\n    &::file-selector-button {\\n      display: inline-flex;\\n    }\\n  }\\n  .file\\\\:h-7 {\\n    &::file-selector-button {\\n      height: calc(var(--spacing) * 7);\\n    }\\n  }\\n  .file\\\\:border-0 {\\n    &::file-selector-button {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .file\\\\:bg-transparent {\\n    &::file-selector-button {\\n      background-color: transparent;\\n    }\\n  }\\n  .file\\\\:text-sm {\\n    &::file-selector-button {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .file\\\\:font-medium {\\n    &::file-selector-button {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .file\\\\:text-foreground {\\n    &::file-selector-button {\\n      color: var(--foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-muted-foreground {\\n    &::placeholder {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .placeholder\\\\:text-slate-400 {\\n    &::placeholder {\\n      color: var(--color-slate-400);\\n    }\\n  }\\n  .after\\\\:absolute {\\n    &::after {\\n      content: var(--tw-content);\\n      position: absolute;\\n    }\\n  }\\n  .after\\\\:-inset-2 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .after\\\\:inset-y-0 {\\n    &::after {\\n      content: var(--tw-content);\\n      inset-block: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .after\\\\:left-1\\\\/2 {\\n    &::after {\\n      content: var(--tw-content);\\n      left: calc(1/2 * 100%);\\n    }\\n  }\\n  .after\\\\:w-\\\\[2px\\\\] {\\n    &::after {\\n      content: var(--tw-content);\\n      width: 2px;\\n    }\\n  }\\n  .group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:after\\\\:left-full {\\n    &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n      &::after {\\n        content: var(--tw-content);\\n        left: 100%;\\n      }\\n    }\\n  }\\n  .last\\\\:border-b-0 {\\n    &:last-child {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 0px;\\n    }\\n  }\\n  .hover\\\\:border-slate-300 {\\n    &:hover {\\n      @media (hover: hover) {\\n        border-color: var(--color-slate-300);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-accent\\\\/50 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--accent);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--accent) 50%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-destructive\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-green-600\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 90%, transparent);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--color-green-600) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-green-700 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-green-700);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-muted\\\\/50 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--muted);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-primary {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--primary);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-primary\\\\/90 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--primary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-secondary\\\\/80 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--secondary);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-sidebar-accent {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--sidebar-accent);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-white\\\\/10 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: color-mix(in srgb, #fff 10%, transparent);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:text-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-sidebar-accent-foreground {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--sidebar-accent-foreground);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-white {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--color-white);\\n      }\\n    }\\n  }\\n  .hover\\\\:underline {\\n    &:hover {\\n      @media (hover: hover) {\\n        text-decoration-line: underline;\\n      }\\n    }\\n  }\\n  .hover\\\\:opacity-100 {\\n    &:hover {\\n      @media (hover: hover) {\\n        opacity: 100%;\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-\\\\[0_0_0_1px_hsl\\\\(var\\\\(--sidebar-accent\\\\)\\\\)\\\\] {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .hover\\\\:group-data-\\\\[collapsible\\\\=offcanvas\\\\]\\\\:bg-sidebar {\\n    &:hover {\\n      @media (hover: hover) {\\n        &:is(:where(.group)[data-collapsible=\\\"offcanvas\\\"] *) {\\n          background-color: var(--sidebar);\\n        }\\n      }\\n    }\\n  }\\n  .hover\\\\:after\\\\:bg-sidebar-border {\\n    &:hover {\\n      @media (hover: hover) {\\n        &::after {\\n          content: var(--tw-content);\\n          background-color: var(--sidebar-border);\\n        }\\n      }\\n    }\\n  }\\n  .focus\\\\:border-primary {\\n    &:focus {\\n      border-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:border-slate-400 {\\n    &:focus {\\n      border-color: var(--color-slate-400);\\n    }\\n  }\\n  .focus\\\\:bg-accent {\\n    &:focus {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .focus\\\\:bg-green-500 {\\n    &:focus {\\n      background-color: var(--color-green-500);\\n    }\\n  }\\n  .focus\\\\:text-accent-foreground {\\n    &:focus {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .focus\\\\:shadow {\\n    &:focus {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:shadow-none {\\n    &:focus {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-2 {\\n    &:focus {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-indigo-500 {\\n    &:focus {\\n      --tw-ring-color: var(--color-indigo-500);\\n    }\\n  }\\n  .focus\\\\:ring-primary {\\n    &:focus {\\n      --tw-ring-color: var(--primary);\\n    }\\n  }\\n  .focus\\\\:ring-ring {\\n    &:focus {\\n      --tw-ring-color: var(--ring);\\n    }\\n  }\\n  .focus\\\\:ring-offset-2 {\\n    &:focus {\\n      --tw-ring-offset-width: 2px;\\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n    }\\n  }\\n  .focus\\\\:outline-hidden {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n      @media (forced-colors: active) {\\n        outline: 2px solid transparent;\\n        outline-offset: 2px;\\n      }\\n    }\\n  }\\n  .focus\\\\:outline-none {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n    }\\n  }\\n  .focus-visible\\\\:border-ring {\\n    &:focus-visible {\\n      border-color: var(--ring);\\n    }\\n  }\\n  .focus-visible\\\\:ring-2 {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-\\\\[3px\\\\] {\\n    &:focus-visible {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus-visible\\\\:ring-destructive\\\\/20 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:ring-ring\\\\/50 {\\n    &:focus-visible {\\n      --tw-ring-color: var(--ring);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n      }\\n    }\\n  }\\n  .focus-visible\\\\:outline-1 {\\n    &:focus-visible {\\n      outline-style: var(--tw-outline-style);\\n      outline-width: 1px;\\n    }\\n  }\\n  .focus-visible\\\\:outline-ring {\\n    &:focus-visible {\\n      outline-color: var(--ring);\\n    }\\n  }\\n  .active\\\\:bg-green-700 {\\n    &:active {\\n      background-color: var(--color-green-700);\\n    }\\n  }\\n  .active\\\\:bg-sidebar-accent {\\n    &:active {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .active\\\\:bg-white\\\\/10 {\\n    &:active {\\n      background-color: color-mix(in srgb, #fff 10%, transparent);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\\n      }\\n    }\\n  }\\n  .active\\\\:text-sidebar-accent-foreground {\\n    &:active {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .active\\\\:shadow-none {\\n    &:active {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .disabled\\\\:pointer-events-none {\\n    &:disabled {\\n      pointer-events: none;\\n    }\\n  }\\n  .disabled\\\\:cursor-not-allowed {\\n    &:disabled {\\n      cursor: not-allowed;\\n    }\\n  }\\n  .disabled\\\\:opacity-50 {\\n    &:disabled {\\n      opacity: 50%;\\n    }\\n  }\\n  .disabled\\\\:shadow-none {\\n    &:disabled {\\n      --tw-shadow: 0 0 #0000;\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=left\\\\]\\\\:cursor-w-resize {\\n    :where(*[data-side=\\\"left\\\"]) & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .in-data-\\\\[side\\\\=right\\\\]\\\\:cursor-e-resize {\\n    :where(*[data-side=\\\"right\\\"]) & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .has-data-\\\\[slot\\\\=card-action\\\\]\\\\:grid-cols-\\\\[1fr_auto\\\\] {\\n    &:has(*[data-slot=\\\"card-action\\\"]) {\\n      grid-template-columns: 1fr auto;\\n    }\\n  }\\n  .has-data-\\\\[variant\\\\=inset\\\\]\\\\:bg-sidebar {\\n    &:has(*[data-variant=\\\"inset\\\"]) {\\n      background-color: var(--sidebar);\\n    }\\n  }\\n  .has-\\\\[\\\\[aria-checked\\\\=true\\\\]\\\\]\\\\:border-yellow-500 {\\n    &:has(*:is([aria-checked=true])) {\\n      border-color: var(--color-yellow-500);\\n    }\\n  }\\n  .has-\\\\[\\\\[aria-checked\\\\=true\\\\]\\\\]\\\\:bg-yellow-50 {\\n    &:has(*:is([aria-checked=true])) {\\n      background-color: var(--color-yellow-50);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-2\\\\.5 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-3 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .has-\\\\[\\\\>svg\\\\]\\\\:px-4 {\\n    &:has(>svg) {\\n      padding-inline: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .aria-disabled\\\\:pointer-events-none {\\n    &[aria-disabled=\\\"true\\\"] {\\n      pointer-events: none;\\n    }\\n  }\\n  .aria-disabled\\\\:opacity-50 {\\n    &[aria-disabled=\\\"true\\\"] {\\n      opacity: 50%;\\n    }\\n  }\\n  .aria-invalid\\\\:border-destructive {\\n    &[aria-invalid=\\\"true\\\"] {\\n      border-color: var(--destructive);\\n    }\\n  }\\n  .aria-invalid\\\\:ring-destructive\\\\/20 {\\n    &[aria-invalid=\\\"true\\\"] {\\n      --tw-ring-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n      }\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:bg-sidebar-accent {\\n    &[data-active=\\\"true\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:font-medium {\\n    &[data-active=\\\"true\\\"] {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .data-\\\\[active\\\\=true\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-active=\\\"true\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:pointer-events-none {\\n    &[data-disabled] {\\n      pointer-events: none;\\n    }\\n  }\\n  .data-\\\\[disabled\\\\]\\\\:opacity-50 {\\n    &[data-disabled] {\\n      opacity: 50%;\\n    }\\n  }\\n  .data-\\\\[disabled\\\\=true\\\\]\\\\:pointer-events-none {\\n    &[data-disabled=\\\"true\\\"] {\\n      pointer-events: none;\\n    }\\n  }\\n  .data-\\\\[disabled\\\\=true\\\\]\\\\:opacity-50 {\\n    &[data-disabled=\\\"true\\\"] {\\n      opacity: 50%;\\n    }\\n  }\\n  .data-\\\\[inset\\\\]\\\\:pl-8 {\\n    &[data-inset] {\\n      padding-left: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:h-px {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      height: 1px;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=horizontal\\\\]\\\\:w-full {\\n    &[data-orientation=\\\"horizontal\\\"] {\\n      width: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:h-full {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      height: 100%;\\n    }\\n  }\\n  .data-\\\\[orientation\\\\=vertical\\\\]\\\\:w-px {\\n    &[data-orientation=\\\"vertical\\\"] {\\n      width: 1px;\\n    }\\n  }\\n  .data-\\\\[placeholder\\\\]\\\\:text-muted-foreground {\\n    &[data-placeholder] {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .data-\\\\[selected\\\\=true\\\\]\\\\:bg-accent {\\n    &[data-selected=\\\"true\\\"] {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .data-\\\\[selected\\\\=true\\\\]\\\\:text-accent-foreground {\\n    &[data-selected=\\\"true\\\"] {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2 {\\n    &[data-side=\\\"bottom\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2 {\\n    &[data-side=\\\"left\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-translate-x: calc(var(--spacing) * 1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2 {\\n    &[data-side=\\\"right\\\"] {\\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-translate-y: calc(var(--spacing) * -1);\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2 {\\n    &[data-side=\\\"top\\\"] {\\n      --tw-enter-translate-y: calc(2*var(--spacing));\\n    }\\n  }\\n  .data-\\\\[size\\\\=default\\\\]\\\\:h-9 {\\n    &[data-size=\\\"default\\\"] {\\n      height: calc(var(--spacing) * 9);\\n    }\\n  }\\n  .data-\\\\[size\\\\=sm\\\\]\\\\:h-8 {\\n    &[data-size=\\\"sm\\\"] {\\n      height: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .\\\\*\\\\*\\\\:data-\\\\[slot\\\\=command-input-wrapper\\\\]\\\\:h-12 {\\n    :is(& *) {\\n      &[data-slot=\\\"command-input-wrapper\\\"] {\\n        height: calc(var(--spacing) * 12);\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:line-clamp-1 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        overflow: hidden;\\n        display: -webkit-box;\\n        -webkit-box-orient: vertical;\\n        -webkit-line-clamp: 1;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:flex {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        display: flex;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:items-center {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        align-items: center;\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:data-\\\\[slot\\\\=select-value\\\\]\\\\:gap-2 {\\n    :is(& > *) {\\n      &[data-slot=\\\"select-value\\\"] {\\n        gap: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .data-\\\\[slot\\\\=sidebar-menu-button\\\\]\\\\:\\\\!p-1\\\\.5 {\\n    &[data-slot=\\\"sidebar-menu-button\\\"] {\\n      padding: calc(var(--spacing) * 1.5) !important;\\n    }\\n  }\\n  .data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm {\\n    &[data-state=\\\"active\\\"] {\\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:border-primary {\\n    &[data-state=\\\"checked\\\"] {\\n      border-color: var(--primary);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:border-yellow-500 {\\n    &[data-state=\\\"checked\\\"] {\\n      border-color: var(--color-yellow-500);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary {\\n    &[data-state=\\\"checked\\\"] {\\n      background-color: var(--primary);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:bg-yellow-500 {\\n    &[data-state=\\\"checked\\\"] {\\n      background-color: var(--color-yellow-500);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground {\\n    &[data-state=\\\"checked\\\"] {\\n      color: var(--primary-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=checked\\\\]\\\\:text-white {\\n    &[data-state=\\\"checked\\\"] {\\n      color: var(--color-white);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-accordion-up {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:animate-out {\\n    &[data-state=\\\"closed\\\"] {\\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:duration-300 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-duration: 300ms;\\n      transition-duration: 300ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-opacity: calc(0/100);\\n      --tw-exit-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95 {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-scale: calc(95*1%);\\n      --tw-exit-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-bottom {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-right {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top {\\n    &[data-state=\\\"closed\\\"] {\\n      --tw-exit-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-accordion-down {\\n    &[data-state=\\\"open\\\"] {\\n      animation: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:animate-in {\\n    &[data-state=\\\"open\\\"] {\\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-secondary {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--secondary);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      background-color: var(--sidebar-accent);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:opacity-100 {\\n    &[data-state=\\\"open\\\"] {\\n      opacity: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:duration-500 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-duration: 500ms;\\n      transition-duration: 500ms;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-opacity: calc(0/100);\\n      --tw-enter-opacity: 0;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95 {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-scale: calc(95*1%);\\n      --tw-enter-scale: .95;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-bottom {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-right {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-x: 100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top {\\n    &[data-state=\\\"open\\\"] {\\n      --tw-enter-translate-y: -100%;\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:bg-sidebar-accent {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--sidebar-accent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=open\\\\]\\\\:hover\\\\:text-sidebar-accent-foreground {\\n    &[data-state=\\\"open\\\"] {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--sidebar-accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted {\\n    &[data-state=\\\"selected\\\"] {\\n      background-color: var(--muted);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      color: var(--destructive);\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/10 {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        background-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      &:focus {\\n        color: var(--destructive);\\n      }\\n    }\\n  }\\n  .sm\\\\:mx-auto {\\n    @media (width >= 40rem) {\\n      margin-inline: auto;\\n    }\\n  }\\n  .sm\\\\:block {\\n    @media (width >= 40rem) {\\n      display: block;\\n    }\\n  }\\n  .sm\\\\:flex {\\n    @media (width >= 40rem) {\\n      display: flex;\\n    }\\n  }\\n  .sm\\\\:w-full {\\n    @media (width >= 40rem) {\\n      width: 100%;\\n    }\\n  }\\n  .sm\\\\:max-w-lg {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-lg);\\n    }\\n  }\\n  .sm\\\\:max-w-md {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-md);\\n    }\\n  }\\n  .sm\\\\:max-w-sm {\\n    @media (width >= 40rem) {\\n      max-width: var(--container-sm);\\n    }\\n  }\\n  .sm\\\\:flex-row {\\n    @media (width >= 40rem) {\\n      flex-direction: row;\\n    }\\n  }\\n  .sm\\\\:justify-end {\\n    @media (width >= 40rem) {\\n      justify-content: flex-end;\\n    }\\n  }\\n  .sm\\\\:gap-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      gap: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:px-6 {\\n    @media (width >= 40rem) {\\n      padding-inline: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .sm\\\\:pr-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-right: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:pl-2\\\\.5 {\\n    @media (width >= 40rem) {\\n      padding-left: calc(var(--spacing) * 2.5);\\n    }\\n  }\\n  .sm\\\\:text-left {\\n    @media (width >= 40rem) {\\n      text-align: left;\\n    }\\n  }\\n  .sm\\\\:text-sm {\\n    @media (width >= 40rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:block {\\n    @media (width >= 48rem) {\\n      display: block;\\n    }\\n  }\\n  .md\\\\:flex {\\n    @media (width >= 48rem) {\\n      display: flex;\\n    }\\n  }\\n  .md\\\\:text-sm {\\n    @media (width >= 48rem) {\\n      font-size: var(--text-sm);\\n      line-height: var(--tw-leading, var(--text-sm--line-height));\\n    }\\n  }\\n  .md\\\\:opacity-0 {\\n    @media (width >= 48rem) {\\n      opacity: 0%;\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:m-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin: calc(var(--spacing) * 2);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:ml-0 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        margin-left: calc(var(--spacing) * 0);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:rounded-xl {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        border-radius: calc(var(--radius) + 4px);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:shadow-sm {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .md\\\\:peer-data-\\\\[variant\\\\=inset\\\\]\\\\:peer-data-\\\\[state\\\\=collapsed\\\\]\\\\:ml-2 {\\n    @media (width >= 48rem) {\\n      &:is(:where(.peer)[data-variant=\\\"inset\\\"] ~ *) {\\n        &:is(:where(.peer)[data-state=\\\"collapsed\\\"] ~ *) {\\n          margin-left: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .md\\\\:after\\\\:hidden {\\n    @media (width >= 48rem) {\\n      &::after {\\n        content: var(--tw-content);\\n        display: none;\\n      }\\n    }\\n  }\\n  .lg\\\\:px-8 {\\n    @media (width >= 64rem) {\\n      padding-inline: calc(var(--spacing) * 8);\\n    }\\n  }\\n  .dark\\\\:border-input {\\n    &:is(.dark *) {\\n      border-color: var(--input);\\n    }\\n  }\\n  .dark\\\\:bg-destructive\\\\/60 {\\n    &:is(.dark *) {\\n      background-color: var(--destructive);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      background-color: var(--input);\\n      @supports (color: color-mix(in lab, red, red)) {\\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n      }\\n    }\\n  }\\n  .dark\\\\:text-muted-foreground {\\n    &:is(.dark *) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-accent\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:hover\\\\:bg-input\\\\/50 {\\n    &:is(.dark *) {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--input);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:focus-visible\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &:focus-visible {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:has-\\\\[\\\\[aria-checked\\\\=true\\\\]\\\\]\\\\:border-yellow-500 {\\n    &:is(.dark *) {\\n      &:has(*:is([aria-checked=true])) {\\n        border-color: var(--color-yellow-500);\\n      }\\n    }\\n  }\\n  .dark\\\\:has-\\\\[\\\\[aria-checked\\\\=true\\\\]\\\\]\\\\:bg-yellow-400 {\\n    &:is(.dark *) {\\n      &:has(*:is([aria-checked=true])) {\\n        background-color: var(--color-yellow-400);\\n      }\\n    }\\n  }\\n  .dark\\\\:aria-invalid\\\\:ring-destructive\\\\/40 {\\n    &:is(.dark *) {\\n      &[aria-invalid=\\\"true\\\"] {\\n        --tw-ring-color: var(--destructive);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:border-input {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        border-color: var(--input);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:bg-input\\\\/30 {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        background-color: var(--input);\\n        @supports (color: color-mix(in lab, red, red)) {\\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\\n        }\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=active\\\\]\\\\:text-foreground {\\n    &:is(.dark *) {\\n      &[data-state=\\\"active\\\"] {\\n        color: var(--foreground);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=checked\\\\]\\\\:border-yellow-500 {\\n    &:is(.dark *) {\\n      &[data-state=\\\"checked\\\"] {\\n        border-color: var(--color-yellow-500);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary {\\n    &:is(.dark *) {\\n      &[data-state=\\\"checked\\\"] {\\n        background-color: var(--primary);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[state\\\\=checked\\\\]\\\\:bg-yellow-500 {\\n    &:is(.dark *) {\\n      &[data-state=\\\"checked\\\"] {\\n        background-color: var(--color-yellow-500);\\n      }\\n    }\\n  }\\n  .dark\\\\:data-\\\\[variant\\\\=destructive\\\\]\\\\:focus\\\\:bg-destructive\\\\/20 {\\n    &:is(.dark *) {\\n      &[data-variant=\\\"destructive\\\"] {\\n        &:focus {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:px-2 {\\n    & [cmdk-group-heading] {\\n      padding-inline: calc(var(--spacing) * 2);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:py-1\\\\.5 {\\n    & [cmdk-group-heading] {\\n      padding-block: calc(var(--spacing) * 1.5);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-xs {\\n    & [cmdk-group-heading] {\\n      font-size: var(--text-xs);\\n      line-height: var(--tw-leading, var(--text-xs--line-height));\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:font-medium {\\n    & [cmdk-group-heading] {\\n      --tw-font-weight: var(--font-weight-medium);\\n      font-weight: var(--font-weight-medium);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group-heading\\\\]\\\\]\\\\:text-muted-foreground {\\n    & [cmdk-group-heading] {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\]\\\\:px-2 {\\n    & [cmdk-group] {\\n      padding-inline: calc(var(--spacing) * 2);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-group\\\\]\\\\:not\\\\(\\\\[hidden\\\\]\\\\)_\\\\~\\\\[cmdk-group\\\\]\\\\]\\\\:pt-0 {\\n    & [cmdk-group]:not([hidden]) ~[cmdk-group] {\\n      padding-top: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:h-5 {\\n    & [cmdk-input-wrapper] svg {\\n      height: calc(var(--spacing) * 5);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-input-wrapper\\\\]_svg\\\\]\\\\:w-5 {\\n    & [cmdk-input-wrapper] svg {\\n      width: calc(var(--spacing) * 5);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-input\\\\]\\\\]\\\\:h-12 {\\n    & [cmdk-input] {\\n      height: calc(var(--spacing) * 12);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:px-2 {\\n    & [cmdk-item] {\\n      padding-inline: calc(var(--spacing) * 2);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-item\\\\]\\\\]\\\\:py-3 {\\n    & [cmdk-item] {\\n      padding-block: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:h-5 {\\n    & [cmdk-item] svg {\\n      height: calc(var(--spacing) * 5);\\n    }\\n  }\\n  .\\\\[\\\\&_\\\\[cmdk-item\\\\]_svg\\\\]\\\\:w-5 {\\n    & [cmdk-item] svg {\\n      width: calc(var(--spacing) * 5);\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:pointer-events-none {\\n    & svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\]\\\\:shrink-0 {\\n    & svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'size-\\\\'\\\\]\\\\)\\\\]\\\\:size-4 {\\n    & svg:not([class*='size-']) {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&_svg\\\\:not\\\\(\\\\[class\\\\*\\\\=\\\\'text-\\\\'\\\\]\\\\)\\\\]\\\\:text-muted-foreground {\\n    & svg:not([class*='text-']) {\\n      color: var(--muted-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\]\\\\:border-b {\\n    & tr {\\n      border-bottom-style: var(--tw-border-style);\\n      border-bottom-width: 1px;\\n    }\\n  }\\n  .\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 {\\n    & tr:last-child {\\n      border-style: var(--tw-border-style);\\n      border-width: 0px;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0 {\\n    &:has([role=checkbox]) {\\n      padding-right: calc(var(--spacing) * 0);\\n    }\\n  }\\n  .\\\\[\\\\.border-b\\\\]\\\\:pb-3 {\\n    &:is(.border-b) {\\n      padding-bottom: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\.border-t\\\\]\\\\:pt-6 {\\n    &:is(.border-t) {\\n      padding-top: calc(var(--spacing) * 6);\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:flex {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          display: flex;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:items-center {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          align-items: center;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\*\\\\:\\\\[span\\\\]\\\\:last\\\\:gap-2 {\\n    :is(& > *) {\\n      &:is(span) {\\n        &:last-child {\\n          gap: calc(var(--spacing) * 2);\\n        }\\n      }\\n    }\\n  }\\n  .data-\\\\[variant\\\\=destructive\\\\]\\\\:\\\\*\\\\:\\\\[svg\\\\]\\\\:\\\\!text-destructive {\\n    &[data-variant=\\\"destructive\\\"] {\\n      :is(& > *) {\\n        &:is(svg) {\\n          color: var(--destructive) !important;\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>\\\\[role\\\\=checkbox\\\\]\\\\]\\\\:translate-y-\\\\[2px\\\\] {\\n    &>[role=checkbox] {\\n      --tw-translate-y: 2px;\\n      translate: var(--tw-translate-x) var(--tw-translate-y);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>button\\\\]\\\\:hidden {\\n    &>button {\\n      display: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>span\\\\:last-child\\\\]\\\\:truncate {\\n    &>span:last-child {\\n      overflow: hidden;\\n      text-overflow: ellipsis;\\n      white-space: nowrap;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:pointer-events-none {\\n    &>svg {\\n      pointer-events: none;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3);\\n      height: calc(var(--spacing) * 3);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-3\\\\.5 {\\n    &>svg {\\n      width: calc(var(--spacing) * 3.5);\\n      height: calc(var(--spacing) * 3.5);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:size-4 {\\n    &>svg {\\n      width: calc(var(--spacing) * 4);\\n      height: calc(var(--spacing) * 4);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:shrink-0 {\\n    &>svg {\\n      flex-shrink: 0;\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>svg\\\\]\\\\:text-sidebar-accent-foreground {\\n    &>svg {\\n      color: var(--sidebar-accent-foreground);\\n    }\\n  }\\n  .\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0 {\\n    &>tr {\\n      &:last-child {\\n        border-bottom-style: var(--tw-border-style);\\n        border-bottom-width: 0px;\\n      }\\n    }\\n  }\\n  .\\\\[\\\\&\\\\[data-state\\\\=open\\\\]\\\\>svg\\\\]\\\\:rotate-180 {\\n    &[data-state=open]>svg {\\n      rotate: 180deg;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-right-2 {\\n    [data-side=left][data-collapsible=offcanvas] & {\\n      right: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=left\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-e-resize {\\n    [data-side=left][data-state=collapsed] & {\\n      cursor: e-resize;\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-collapsible\\\\=offcanvas\\\\]_\\\\&\\\\]\\\\:-left-2 {\\n    [data-side=right][data-collapsible=offcanvas] & {\\n      left: calc(var(--spacing) * -2);\\n    }\\n  }\\n  .\\\\[\\\\[data-side\\\\=right\\\\]\\\\[data-state\\\\=collapsed\\\\]_\\\\&\\\\]\\\\:cursor-w-resize {\\n    [data-side=right][data-state=collapsed] & {\\n      cursor: w-resize;\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-accent {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--accent);\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-destructive\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--destructive);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-primary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--primary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:bg-secondary\\\\/90 {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          background-color: var(--secondary);\\n          @supports (color: color-mix(in lab, red, red)) {\\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\\n          }\\n        }\\n      }\\n    }\\n  }\\n  .\\\\[a\\\\&\\\\]\\\\:hover\\\\:text-accent-foreground {\\n    a& {\\n      &:hover {\\n        @media (hover: hover) {\\n          color: var(--accent-foreground);\\n        }\\n      }\\n    }\\n  }\\n}\\n@property --tw-animation-delay {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0s;\\n}\\n@property --tw-animation-direction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: normal;\\n}\\n@property --tw-animation-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-animation-fill-mode {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: none;\\n}\\n@property --tw-animation-iteration-count {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-enter-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-enter-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-scale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 1;\\n}\\n@property --tw-exit-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-exit-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n@layer base {\\n  * {\\n    border-color: var(--border);\\n    outline-color: var(--ring);\\n    @supports (color: color-mix(in lab, red, red)) {\\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\\n    }\\n  }\\n  body {\\n    background-color: var(--background);\\n    color: var(--foreground);\\n  }\\n}\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n@property --tw-translate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-translate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-rotate-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-rotate-z {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-x {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-skew-y {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-space-x-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-leading {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-tracking {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ordinal {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-slashed-zero {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-figure {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-spacing {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-numeric-fraction {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-outline-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-blur {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-brightness {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-contrast {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-grayscale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-hue-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-invert {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-saturate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-sepia {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-drop-shadow-size {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ease {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-content {\\n  syntax: \\\"*\\\";\\n  initial-value: \\\"\\\";\\n  inherits: false;\\n}\\n@keyframes pulse {\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes enter {\\n  from {\\n    opacity: var(--tw-enter-opacity,1);\\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\\n  }\\n}\\n@keyframes exit {\\n  to {\\n    opacity: var(--tw-exit-opacity,1);\\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\\n  }\\n}\\n@keyframes accordion-down {\\n  from {\\n    height: 0;\\n  }\\n  to {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n}\\n@keyframes accordion-up {\\n  from {\\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));\\n  }\\n  to {\\n    height: 0;\\n  }\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-translate-x: 0;\\n      --tw-translate-y: 0;\\n      --tw-translate-z: 0;\\n      --tw-rotate-x: initial;\\n      --tw-rotate-y: initial;\\n      --tw-rotate-z: initial;\\n      --tw-skew-x: initial;\\n      --tw-skew-y: initial;\\n      --tw-space-x-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-leading: initial;\\n      --tw-font-weight: initial;\\n      --tw-tracking: initial;\\n      --tw-ordinal: initial;\\n      --tw-slashed-zero: initial;\\n      --tw-numeric-figure: initial;\\n      --tw-numeric-spacing: initial;\\n      --tw-numeric-fraction: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-outline-style: solid;\\n      --tw-blur: initial;\\n      --tw-brightness: initial;\\n      --tw-contrast: initial;\\n      --tw-grayscale: initial;\\n      --tw-hue-rotate: initial;\\n      --tw-invert: initial;\\n      --tw-opacity: initial;\\n      --tw-saturate: initial;\\n      --tw-sepia: initial;\\n      --tw-drop-shadow: initial;\\n      --tw-drop-shadow-color: initial;\\n      --tw-drop-shadow-alpha: 100%;\\n      --tw-drop-shadow-size: initial;\\n      --tw-duration: initial;\\n      --tw-ease: initial;\\n      --tw-content: \\\"\\\";\\n      --tw-animation-delay: 0s;\\n      --tw-animation-direction: normal;\\n      --tw-animation-duration: initial;\\n      --tw-animation-fill-mode: none;\\n      --tw-animation-iteration-count: 1;\\n      --tw-enter-opacity: 1;\\n      --tw-enter-rotate: 0;\\n      --tw-enter-scale: 1;\\n      --tw-enter-translate-x: 0;\\n      --tw-enter-translate-y: 0;\\n      --tw-exit-opacity: 1;\\n      --tw-exit-rotate: 0;\\n      --tw-exit-scale: 1;\\n      --tw-exit-translate-x: 0;\\n      --tw-exit-translate-y: 0;\\n    }\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://node_modules/tw-animate-css/dist/tw-animate.css\",\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IAeE,0CAA0C;IA8B1C,6CAA6C;IAI7C,6CAA6C;IAC7C,6CAA6C;IAwB7C,6CAA6C;IAC7C,6CAA6C;IAC7C,6CAA6C;IAsE7C,8CAA8C;IAqE9C,6CAA6C;IAC7C,6CAA6C;IAC7C,4CAA4C;IAG5C,6CAA6C;IAC7C,6CAA6C;IAO7C,2CAA2C;IAC3C,4CAA4C;IAG5C,4CAA4C;IAE5C,0CAA0C;IAuC1C,mBAAmB;IACnB,mBAAmB;IAEnB,kBAAkB;IAWlB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IASrB,kBAAkB;IAClB,sCAAsC;IACtC,mBAAmB;IACnB,0CAA0C;IAC1C,iBAAiB;IACjB,uCAAuC;IACvC,mBAAmB;IACnB,0CAA0C;IAG1C,kBAAkB;IAClB,sCAAsC;IAmBtC,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,uBAAuB;IACvB,4BAA4B;IAI5B,0BAA0B;IAI1B,wBAAwB;IAExB,qBAAqB;IAMrB,qBAAqB;IA6CrB,2CAA2C;IAI3C,+DAA+D;IAoD/D,oCAAoC;IACpC,kEAAkE;IAClE,6CAAoD;IASpD,kDAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,2BAAmB;IAAnB,2BAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,kBAAmB;IAAnB,UAAmB;IAAnB,WAAmB;IAAnB,UAAmB;IAAnB,YAAmB;IAAnB,gBAAmB;IAAnB,sBAAmB;IAAnB,mBAAmB;IAAnB,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,QAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,SAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,iCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,gCAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB,YAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,wDAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,4CAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,qEAAmB;EAAA;EAAnB;IAAA,kEAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,8DAAmB;EAAA;EAAnB;IAAA,+DAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,qBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,sDAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,0GAAmB;EAAA;EAAnB;IAAA,+NAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,6CAAmB;EAAA;EAAnB;IAAA,8CAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,2BAAmB;EAAA;EAAnB;IAAA,8CAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,+EAAmB;MAAnB,uFAAmB;IAAA;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,gBAAmB;IAAnB,uBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,cAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,4CAAmB;IAAnB,+CAAmB;EAAA;EAAnB;IAAA,iDAAmB;EAAA;EAAnB;IAAA,kDAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,wCAAmB;IAAnB,qBAAmB;EAAA;EAAnB;IAAA,0CAAmB;IAAnB,uBAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,wBAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,2DAAmB;IAAnB;MAAA,0EAAmB;IAAA;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,8BAAmB;IAAnB;MAAA,oEAAmB;IAAA;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,oBAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,0CAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,2BAAmB;IAAnB,6DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,eAAmB;IAAnB,cAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iCAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,oCAAmB;EAAA;EAAnB;IAAA,8CAAmB;IAAnB,yCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,qCAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,wBAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,qBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;IAAnB;MAAA,sEAAmB;IAAA;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,kCAAmB;IAAnB,iJAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,kCAAmB;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,YAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,yEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,+HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,sBAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,0HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,kEAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;IAAnB;MAAA,8BAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA,sCAAmB;IAAnB,kBAAmB;EAAA;EAAnB;IAAA,0LAAmB;EAAA;EAAnB;IAAA,qVAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,qCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,mCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wBAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,4BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,wDAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA,6BAAmB;IAAnB,8CAAmB;EAAA;EAAnB;IAAA,iBAAmB;IAAnB,kCAAmB;EAAA;EAAnB;ICt3B6yL,+BAA6C;IAAE,qBAA+C;EDs3Bx3L;EAAnB;IAAA,wBAAmB;IAAnB,mBAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,iBAAmB;EAAA;EAAnB;ICt3BgmM,6BAA0C;IAA0C,qBAA6C;EDs3B9sM;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,2CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yEAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,uBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;IAAnB;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,6BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,sBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;MAAnB,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,UAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;QAAnB;UAAA,qEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,iFAAmB;QAAnB;UAAA,8EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;QAAnB;UAAA,sEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,kCAAmB;QAAnB;UAAA,wEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2DAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yEAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,0BAAmB;UAAnB,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2BAAmB;MAAnB,4GAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;MAAnB;QAAA,8BAAmB;QAAnB,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;MAAnB;QAAA,kEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;MAAnB,kBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2DAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sBAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mCAAmB;MAAnB;QAAA,yEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,YAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,UAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BklP,iDAAgE;IDs3B/nP;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3BoqR,8CAA6D;IDs3B9sR;EAAA;EAAnB;IAAA;MAAA,0CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B+yQ,iDAAgE;IDs3B51Q;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8P,8CAA6D;IDs3Bn/P;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,iCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gBAAmB;QAAnB,oBAAmB;QAAnB,4BAAmB;QAAnB,qBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,6BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8CAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0HAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,4BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3By8L,8BAA4C;MAAE,oBAA8C;IDs3BlhM;EAAA;EAAnB;IAAA;MCt3Bw7M,4BAAyC;MAAyC,oBAA4C;IDs3BniN;EAAA;EAAnB;IAAA;MCt3Bu2V,2BAA2B;IDs3B/2V;EAAA;EAAnB;IAAA;MCt3BusW,4BAA4B;IDs3BhtW;EAAA;EAAnB;IAAA;MCt3BqjX,2BAA2B;IDs3B7jX;EAAA;EAAnB;IAAA;MCt3By/U,4BAA4B;IDs3BlgV;EAAA;EAAnB;IAAA;MAAA,qFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+NAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;MAAnB,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MCt3B6yL,+BAA6C;MAAE,qBAA+C;IDs3Bx3L;EAAA;EAAnB;IAAA;MCt3BgmM,6BAA0C;MAA0C,qBAA6C;IDs3B9sM;EAAA;EAAnB;IAAA;MCt3B24P,4BAA4B;IDs3Bp5P;EAAA;EAAnB;IAAA;MCt3BkvQ,6BAA6B;IDs3B5vQ;EAAA;EAAnB;IAAA;MCt3BumR,4BAA4B;IDs3BhnR;EAAA;EAAnB;IAAA;MCt3BshP,6BAA6B;IDs3BhiP;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,uCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;QAAnB;UAAA,0EAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,WAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,qCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,qCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;QAAnB,aAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,0BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB;QAAA,0EAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;MAAnB;QAAA,oEAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;UAAnB;YAAA,qEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,8BAAmB;UAAnB;YAAA,oEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,qCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,mCAAmB;QAAnB;UAAA,yEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,0BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,8BAAmB;QAAnB;UAAA,oEAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,qCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,gCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,yCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,yBAAmB;MAAnB,2DAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,iCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,8BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,2CAAmB;MAAnB,wBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oCAAmB;MAAnB,iBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,qCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,aAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,mBAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,6BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,qBAAmB;MAAnB,sDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,aAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;MAAnB,uBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,oBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,iCAAmB;MAAnB,kCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;MAAnB,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,2CAAmB;QAAnB,wBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,cAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,+BAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,oCAAmB;UAAnB;YAAA,0EAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,gCAAmB;UAAnB;YAAA,sEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,kCAAmB;UAAnB;YAAA,wEAAmB;UAAA;QAAA;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA;UAAA,+BAAmB;QAAA;MAAA;IAAA;EAAA;AADJ;ACr3BjB;EAA+B,WAAU;EAAC,eAAc;EAAC,iBAAgB;AAA3C;AAA4C;EAAmC,WAAU;EAAC,eAAc;EAAC,qBAAoB;AAA/C;AAAgD;EAAkC,WAAU;EAAC,eAAc;AAA1B;AAA2B;EAAmC,WAAU;EAAC,eAAc;EAAC,mBAAkB;AAA7C;AAA8C;EAAyC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA6B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAiC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA4B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA2B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAA0B,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;AAA2C;EAAgC,WAAU;EAAC,eAAc;EAAC,gBAAe;AAA1C;ACK3hC;EACE,qBAAsB;EACtB,qBAAsB;EACtB,oBAAqB;EACrB,mCAAoC;EACpC,uBAAwB;EACxB,sCAAuC;EAGvC,sCAAuC;EACvC,sCAAuC;EAEvC,wCAAyC;EAEzC,wCAAyC;EACzC,wBAAyB;EACzB,oCAAqC;EACrC,yBAA0B;EAC1B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,oCAAqC;EACrC,mCAAoC;EACpC,oCAAqC;EACrC,oCAAqC;EACrC,mCAAoC;EACpC,kBAAmB;EACnB,2BAA4B;EAC5B,sCAAuC;EACvC,mCAAoC;EACpC,8CAA+C;EAC/C,iCAAkC;EAClC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;EAEjC,mCAAoC;EACpC,oCAAqC;EACrC,wCAAyC;EACzC,sCAAuC;EACvC,sCAAuC;EACvC,wCAAyC;EACzC,wCAAyC;EACzC,qCAAsC;EACtC,oCAAqC;EACrC,mCAAoC;EACpC,qCAAsC;EACtC,yCAA0C;AAC3C;AAgED;EACE,6BAA8B;EAC9B,wBAAyB;EACzB,yCAA0C;AAC3C;AAED;EACE,8BAA+B;EAC/B,8BAA+B;EAC/B,wBAAyB;EACzB,mCAAoC;EACpC,2BAA4B;EAC5B,sCAAuC;EACvC,2BAA4B;EAC5B,sCAAuC;EACvC,6BAA8B;EAC9B,wCAAyC;EACzC,yBAA0B;EAC1B,oCAAqC;EACrC,0BAA2B;EAC3B,qCAAsC;EACtC,wCAAyC;EACzC,mDAAoD;EACpD,0BAA2B;EAC3B,yBAA0B;EAC1B,wBAAyB;EACzB,qCAAsC;EACtC,mCAAoC;EACpC,mCAAoC;EACpC,mCAAoC;EACpC,oCAAqC;EACrC,2BAA4B;EAC5B,sCAAuC;EACvC,6CAA8C;EAC9C,8CAA+C;EAC/C,kCAAmC;EACnC,6CAA8C;EAC9C,kCAAmC;EACnC,gCAAiC;AAClC;AAED;EACE;IACS,2BAAa;IAAC,0BAAe;IAAf;MAAA,gEAAe;IAAA;EACrC;EACD;IACS,mCAAa;IAAC,wBAAe;EACrC;AACF;AAED;EACE;IACE,8BAA+B;IAC/B,oCAAqC;IACrC,+BAAgC;IAChC,sCAAuC;IACvC,gCAAiC;IACjC,yCAA0C;IAC1C,6BAA8B;IAC9B,iCAAkC;EACnC;EAED;IACE,kCAAmC;IACnC,oCAAqC;IACrC,kCAAmC;IACnC,uCAAwC;IACxC,gCAAiC;IACjC,2CAA4C;IAC5C,gCAAiC;IACjC,iCAAkC;EACnC;AACF;AFsrBC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,iBAAmB;EAAnB,eAAmB;AAAA;AArdjB;EACE;IACE,YAAY;EADV;AADW;ACja0oF;EAAmB;IAAO,kCAAkC;IAAE,qMAAqM;EAA3O;AAAP;AAAsP;EAAkB;IAAK,iCAAiC;IAAE,+LAA+L;EAApO;AAAL;AAAqoB;EAA4B;IAAO,SAAS;EAAX;EAAc;IAAK,+JAA+J;EAAjK;AAAxB;AAA6L;EAA0B;IAAO,+JAA+J;EAAjK;EAAoK;IAAK,SAAS;EAAX;AAA9K;ADs3BxyH;EAAA;IAAA;MAAA,mBAAmB;MAAnB,mBAAmB;MAAnB,mBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,sBAAmB;MAAnB,oBAAmB;MAAnB,oBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,qBAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,qBAAmB;MAAnB,0BAAmB;MAAnB,4BAAmB;MAAnB,6BAAmB;MAAnB,8BAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,yBAAmB;MAAnB,kBAAmB;MAAnB,wBAAmB;MAAnB,sBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,oBAAmB;MAAnB,qBAAmB;MAAnB,sBAAmB;MAAnB,mBAAmB;MAAnB,yBAAmB;MAAnB,+BAAmB;MAAnB,4BAAmB;MAAnB,8BAAmB;MAAnB,sBAAmB;MAAnB,kBAAmB;MAAnB,gBAAmB;MCt3BrB,wBAA8B;MAA4C,gCAAkC;MAAgD,gCAAiC;MAA2B,8BAAkC;MAA8C,iCAAwC;MAA2C,qBAA4B;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,yBAAgC;MAA2C,yBAAgC;MAA2C,oBAA2B;MAA2C,mBAA0B;MAA2C,kBAAyB;MAA2C,wBAA+B;MAA2C,wBAA+B;IDs3BtgC;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@property --tw-animation-delay{syntax:\\\"*\\\";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:\\\"*\\\";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:\\\"*\\\";inherits:false}@property --tw-animation-fill-mode{syntax:\\\"*\\\";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:\\\"*\\\";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:\\\"*\\\";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:\\\"*\\\";inherits:false;initial-value:0}@theme inline{--animation-delay-0: 0s; --animation-delay-75: 75ms; --animation-delay-100: .1s; --animation-delay-150: .15s; --animation-delay-200: .2s; --animation-delay-300: .3s; --animation-delay-500: .5s; --animation-delay-700: .7s; --animation-delay-1000: 1s; --animation-repeat-0: 0; --animation-repeat-1: 1; --animation-repeat-infinite: infinite; --animation-direction-normal: normal; --animation-direction-reverse: reverse; --animation-direction-alternate: alternate; --animation-direction-alternate-reverse: alternate-reverse; --animation-fill-mode-none: none; --animation-fill-mode-forwards: forwards; --animation-fill-mode-backwards: backwards; --animation-fill-mode-both: both; --percentage-0: 0; --percentage-5: .05; --percentage-10: .1; --percentage-15: .15; --percentage-20: .2; --percentage-25: .25; --percentage-30: .3; --percentage-35: .35; --percentage-40: .4; --percentage-45: .45; --percentage-50: .5; --percentage-55: .55; --percentage-60: .6; --percentage-65: .65; --percentage-70: .7; --percentage-75: .75; --percentage-80: .8; --percentage-85: .85; --percentage-90: .9; --percentage-95: .95; --percentage-100: 1; --percentage-translate-full: 1; --animate-in: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); --animate-out: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none); @keyframes enter { from { opacity: var(--tw-enter-opacity,1); transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0)); }}@keyframes exit { to { opacity: var(--tw-exit-opacity,1); transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0)); }}--animate-accordion-down: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-accordion-up: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-down: collapsible-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; --animate-collapsible-up: collapsible-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out; @keyframes accordion-down { from { height: 0; }to { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }}@keyframes accordion-up { from { height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto)))); }to { height: 0; }}@keyframes collapsible-down { from { height: 0; }to { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }}@keyframes collapsible-up { from { height: var(--radix-collapsible-content-height,var(--bits-collapsible-content-height,var(--reka-collapsible-content-height,var(--kb-collapsible-content-height,auto)))); }to { height: 0; }}--animate-caret-blink: caret-blink 1.25s ease-out infinite; @keyframes caret-blink { 0%,70%,100% { opacity: 1; }20%,50% { opacity: 0; }}}@utility animation-duration-*{--tw-animation-duration: calc(--value(number)*1ms); --tw-animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]); animation-duration: calc(--value(number)*1ms); animation-duration: --value(--animation-duration-*,[duration],\\\"initial\\\",[*]);}@utility delay-*{animation-delay: calc(--value(number)*1ms); animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]); --tw-animation-delay: calc(--value(number)*1ms); --tw-animation-delay: --value(--animation-delay-*,[duration],\\\"initial\\\",[*]);}@utility repeat-*{animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]); --tw-animation-iteration-count: --value(--animation-repeat-*,number,\\\"initial\\\",[*]);}@utility direction-*{animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]); --tw-animation-direction: --value(--animation-direction-*,\\\"initial\\\",[*]);}@utility fill-mode-*{animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]); --tw-animation-fill-mode: --value(--animation-fill-mode-*,\\\"initial\\\",[*]);}@utility running{animation-play-state: running;}@utility paused{animation-play-state: paused;}@utility play-state-*{animation-play-state: --value(\\\"initial\\\",[*]);}@utility fade-in{--tw-enter-opacity: 0;}@utility fade-in-*{--tw-enter-opacity: calc(--value(number)/100); --tw-enter-opacity: --value(--percentage-*,[*]);}@utility fade-out{--tw-exit-opacity: 0;}@utility fade-out-*{--tw-exit-opacity: calc(--value(number)/100); --tw-exit-opacity: --value(--percentage-*,[*]);}@utility zoom-in{--tw-enter-scale: 0;}@utility zoom-in-*{--tw-enter-scale: calc(--value(number)*1%); --tw-enter-scale: calc(--value(ratio)); --tw-enter-scale: --value(--percentage-*,[*]);}@utility -zoom-in-*{--tw-enter-scale: calc(--value(number)*-1%); --tw-enter-scale: calc(--value(ratio)*-1); --tw-enter-scale: --value(--percentage-*,[*]);}@utility zoom-out{--tw-exit-scale: 0;}@utility zoom-out-*{--tw-exit-scale: calc(--value(number)*1%); --tw-exit-scale: calc(--value(ratio)); --tw-exit-scale: --value(--percentage-*,[*]);}@utility -zoom-out-*{--tw-exit-scale: calc(--value(number)*-1%); --tw-exit-scale: calc(--value(ratio)*-1); --tw-exit-scale: --value(--percentage-*,[*]);}@utility spin-in{--tw-enter-rotate: 30deg;}@utility spin-in-*{--tw-enter-rotate: calc(--value(number)*1deg); --tw-enter-rotate: calc(--value(ratio)*360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility -spin-in{--tw-enter-rotate: -30deg;}@utility -spin-in-*{--tw-enter-rotate: calc(--value(number)*-1deg); --tw-enter-rotate: calc(--value(ratio)*-360deg); --tw-enter-rotate: --value(--rotate-*,[*]);}@utility spin-out{--tw-exit-rotate: 30deg;}@utility spin-out-*{--tw-exit-rotate: calc(--value(number)*1deg); --tw-exit-rotate: calc(--value(ratio)*360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility -spin-out{--tw-exit-rotate: -30deg;}@utility -spin-out-*{--tw-exit-rotate: calc(--value(number)*-1deg); --tw-exit-rotate: calc(--value(ratio)*-360deg); --tw-exit-rotate: --value(--rotate-*,[*]);}@utility slide-in-from-top{--tw-enter-translate-y: -100%;}@utility slide-in-from-top-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-y: calc(--value(ratio)*-100%); --tw-enter-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-bottom{--tw-enter-translate-y: 100%;}@utility slide-in-from-bottom-*{--tw-enter-translate-y: calc(--value(integer)*var(--spacing)); --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-y: calc(--value(ratio)*100%); --tw-enter-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-left{--tw-enter-translate-x: -100%;}@utility slide-in-from-left-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-in-from-right{--tw-enter-translate-x: 100%;}@utility slide-in-from-right-*{--tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-in-from-start{&:dir(ltr){ --tw-enter-translate-x: -100%; }&:dir(rtl){ --tw-enter-translate-x: 100%; }}@utility slide-in-from-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-in-from-end{&:dir(ltr){ --tw-enter-translate-x: 100%; }&:dir(rtl){ --tw-enter-translate-x: -100%; }}@utility slide-in-from-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-enter-translate-x: calc(--value(ratio)*100%); --tw-enter-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-enter-translate-x: calc(--value(ratio)*-100%); --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}@utility slide-out-to-top{--tw-exit-translate-y: -100%;}@utility slide-out-to-top-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-y: calc(--value(ratio)*-100%); --tw-exit-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-bottom{--tw-exit-translate-y: 100%;}@utility slide-out-to-bottom-*{--tw-exit-translate-y: calc(--value(integer)*var(--spacing)); --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-y: calc(--value(ratio)*100%); --tw-exit-translate-y: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-left{--tw-exit-translate-x: -100%;}@utility slide-out-to-left-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);}@utility slide-out-to-right{--tw-exit-translate-x: 100%;}@utility slide-out-to-right-*{--tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]);}@utility slide-out-to-start{&:dir(ltr){ --tw-exit-translate-x: -100%; }&:dir(rtl){ --tw-exit-translate-x: 100%; }}@utility slide-out-to-start-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }}@utility slide-out-to-end{&:dir(ltr){ --tw-exit-translate-x: 100%; }&:dir(rtl){ --tw-exit-translate-x: -100%; }}@utility slide-out-to-end-*{&:where(:dir(ltr),[dir=\\\"ltr\\\"],[dir=\\\"ltr\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%); --tw-exit-translate-x: calc(--value(ratio)*100%); --tw-exit-translate-x: --value(--translate-*,[percentage],[length]); }&:where(:dir(rtl),[dir=\\\"rtl\\\"],[dir=\\\"rtl\\\"]*){ --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1); --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%); --tw-exit-translate-x: calc(--value(ratio)*-100%); --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1); }}\",\"@import \\\"tailwindcss\\\";\\n@import \\\"tw-animate-css\\\";\\n\\n@custom-variant dark (&:is(.dark *));\\n\\n:root {\\n  --background: #ffffff;\\n  --foreground: #171717;\\n  --card: oklch(1 0 0);\\n  --card-foreground: oklch(0.145 0 0);\\n  --popover: oklch(1 0 0);\\n  --popover-foreground: oklch(0.145 0 0);\\n  /* --primary: oklch(0.205 0 0); */\\n\\n  --primary: oklch(24.37% 0.0951 286.37);\\n  --primary-foreground: oklch(0.985 0 0);\\n  /* --secondary: oklch(0.97 0 0); */\\n  --secondary: oklch(37.34% 0.1397 315.97);\\n\\n  --secondary-foreground: oklch(0.205 0 0);\\n  --muted: oklch(0.97 0 0);\\n  --muted-foreground: oklch(0.556 0 0);\\n  --accent: oklch(0.97 0 0);\\n  --accent-foreground: oklch(0.205 0 0);\\n  --destructive: oklch(0.577 0.245 27.325);\\n  --destructive-foreground: oklch(0.577 0.245 27.325);\\n  --border: oklch(0.922 0 0);\\n  --input: oklch(0.922 0 0);\\n  --ring: oklch(0.708 0 0);\\n  --chart-1: oklch(0.646 0.222 41.116);\\n  --chart-2: oklch(0.6 0.118 184.704);\\n  --chart-3: oklch(0.398 0.07 227.392);\\n  --chart-4: oklch(0.828 0.189 84.429);\\n  --chart-5: oklch(0.769 0.188 70.08);\\n  --radius: 0.625rem;\\n  --sidebar: oklch(0.985 0 0);\\n  --sidebar-foreground: oklch(0.145 0 0);\\n  --sidebar-primary: oklch(0.205 0 0);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.97 0 0);\\n  --sidebar-accent-foreground: oklch(0.205 0 0);\\n  --sidebar-border: oklch(0.922 0 0);\\n  --sidebar-ring: oklch(0.708 0 0);\\n\\n  --dccpink: oklch(0.54 0.216689 5.2);\\n  --dccblue: oklch(0.24 0.0951 286.37);\\n  --dcclightblue: oklch(0.64 0.1154 218.6);\\n  --dccviolet: oklch(0.45 0.1883 326.95);\\n  --dccpurple: oklch(0.37 0.1397 315.97);\\n  --dcclightgrey: oklch(0.7 0.0146 134.93);\\n  --dccdarkgrey: oklch(0.44 0.0031 228.84);\\n  --dccyellow: oklch(0.87 0.1768 90.38);\\n  --dccgreen: oklch(0.75 0.1806 124.9);\\n  --dccgrey: oklch(0.7 0.0146 134.93);\\n  --dccorange: oklch(0.75 0.1674 64.79);\\n  --dcclightorange: oklch(0.83 0.1464 73.9);\\n}\\n\\n@theme inline {\\n  --color-background: var(--background);\\n  --color-foreground: var(--foreground);\\n  --font-sans: var(--font-geist-sans);\\n  --font-mono: var(--font-geist-mono);\\n  --color-sidebar-ring: var(--sidebar-ring);\\n  --color-sidebar-border: var(--sidebar-border);\\n  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);\\n  --color-sidebar-accent: var(--sidebar-accent);\\n  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);\\n  --color-sidebar-primary: var(--sidebar-primary);\\n  --color-sidebar-foreground: var(--sidebar-foreground);\\n  --color-sidebar: var(--sidebar);\\n  --color-chart-5: var(--chart-5);\\n  --color-chart-4: var(--chart-4);\\n  --color-chart-3: var(--chart-3);\\n  --color-chart-2: var(--chart-2);\\n  --color-chart-1: var(--chart-1);\\n  --color-ring: var(--ring);\\n  --color-input: var(--input);\\n  --color-border: var(--border);\\n  --color-destructive-foreground: var(--destructive-foreground);\\n  --color-destructive: var(--destructive);\\n  --color-accent-foreground: var(--accent-foreground);\\n  --color-accent: var(--accent);\\n  --color-muted-foreground: var(--muted-foreground);\\n  --color-muted: var(--muted);\\n  --color-secondary-foreground: var(--secondary-foreground);\\n  --color-secondary: var(--secondary);\\n  --color-primary-foreground: var(--primary-foreground);\\n  --color-primary: var(--primary);\\n  --color-popover-foreground: var(--popover-foreground);\\n  --color-popover: var(--popover);\\n  --color-card-foreground: var(--card-foreground);\\n  --color-card: var(--card);\\n  --radius-sm: calc(var(--radius) - 4px);\\n  --radius-md: calc(var(--radius) - 2px);\\n  --radius-lg: var(--radius);\\n  --radius-xl: calc(var(--radius) + 4px);\\n\\n  --color-dccpink: var(--dccpink);\\n  --color-dccblue: var(--dccblue);\\n  --color-dcclightblue: var(--dcclightblue);\\n  --color-dccviolet: var(--dccviolet);\\n  --color-dccpurple: var(--dccpurple);\\n  --color-dcclightgrey: var(--dcclightgrey);\\n  --color-dccyellow: var(--dccyellow);\\n  --color-dccgreen: var(--dccgreen);\\n  --color-dccgrey: var(--dccgrey);\\n  --color-dccorange: var(--dccorange);\\n  --color-dcclightorange: var(--dcclightorange);\\n  --color-dccdarkgrey: var(--dccdarkgrey);\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  :root {\\n    /* --background: #ededed; */\\n    /* --background: #0a0a0a; */\\n    /* --foreground: #ededed; */\\n  }\\n}\\n\\nbody {\\n  background: var(--background);\\n  color: var(--foreground);\\n  font-family: Arial, Helvetica, sans-serif;\\n}\\n\\n.dark {\\n  --background: oklch(0.145 0 0);\\n  --foreground: oklch(0.985 0 0);\\n  --card: oklch(0.145 0 0);\\n  --card-foreground: oklch(0.985 0 0);\\n  --popover: oklch(0.145 0 0);\\n  --popover-foreground: oklch(0.985 0 0);\\n  --primary: oklch(0.985 0 0);\\n  --primary-foreground: oklch(0.205 0 0);\\n  --secondary: oklch(0.269 0 0);\\n  --secondary-foreground: oklch(0.985 0 0);\\n  --muted: oklch(0.269 0 0);\\n  --muted-foreground: oklch(0.708 0 0);\\n  --accent: oklch(0.269 0 0);\\n  --accent-foreground: oklch(0.985 0 0);\\n  --destructive: oklch(0.396 0.141 25.723);\\n  --destructive-foreground: oklch(0.637 0.237 25.331);\\n  --border: oklch(0.269 0 0);\\n  --input: oklch(0.269 0 0);\\n  --ring: oklch(0.439 0 0);\\n  --chart-1: oklch(0.488 0.243 264.376);\\n  --chart-2: oklch(0.696 0.17 162.48);\\n  --chart-3: oklch(0.769 0.188 70.08);\\n  --chart-4: oklch(0.627 0.265 303.9);\\n  --chart-5: oklch(0.645 0.246 16.439);\\n  --sidebar: oklch(0.205 0 0);\\n  --sidebar-foreground: oklch(0.985 0 0);\\n  --sidebar-primary: oklch(0.488 0.243 264.376);\\n  --sidebar-primary-foreground: oklch(0.985 0 0);\\n  --sidebar-accent: oklch(0.269 0 0);\\n  --sidebar-accent-foreground: oklch(0.985 0 0);\\n  --sidebar-border: oklch(0.269 0 0);\\n  --sidebar-ring: oklch(0.439 0 0);\\n}\\n\\n@layer base {\\n  * {\\n    @apply border-border outline-ring/50;\\n  }\\n  body {\\n    @apply bg-background text-foreground;\\n  }\\n}\\n\\n@layer base {\\n  :root {\\n    --sidebar-background: 0 0% 98%;\\n    --sidebar-foreground: 240 5.3% 26.1%;\\n    --sidebar-primary: 240 5.9% 10%;\\n    --sidebar-primary-foreground: 0 0% 98%;\\n    --sidebar-accent: 240 4.8% 95.9%;\\n    --sidebar-accent-foreground: 240 5.9% 10%;\\n    --sidebar-border: 220 13% 91%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n\\n  .dark {\\n    --sidebar-background: 240 5.9% 10%;\\n    --sidebar-foreground: 240 4.8% 95.9%;\\n    --sidebar-primary: 224.3 76.3% 48%;\\n    --sidebar-primary-foreground: 0 0% 100%;\\n    --sidebar-accent: 240 3.7% 15.9%;\\n    --sidebar-accent-foreground: 240 4.8% 95.9%;\\n    --sidebar-border: 240 3.7% 15.9%;\\n    --sidebar-ring: 217.2 91.2% 59.8%;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});