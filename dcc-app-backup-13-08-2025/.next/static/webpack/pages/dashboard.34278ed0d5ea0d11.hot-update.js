"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./pages/dashboard.js":
/*!****************************!*\
  !*** ./pages/dashboard.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ console.log(\"tech_data_user\");\n    console.log(tech_data_user);\n    console.log(\"userData\");\n    console.log(userData && userData.role);\n    // const filteredTechData =\n    //   userData && tech_data_user\n    //     ? tech_data_user.filter(\n    //         (item) => item.role_mapping && item.role_mapping.id === userData.role\n    //       )\n    //     : [];\n    const filteredTechData = tech_data_user.filter((el)=>el.role_mapping.map((n)=>n.role_id === user).length > 0);\n    console.log(\"filteredTechData\");\n    console.log(filteredTechData);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // filter the array based on user role\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user: user1 } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user1) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user1.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                href: \"#\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                            className: \"hidden md:block\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                children: \"My Skills Snapshot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"KNaG7HRFKj3fWN5peDVgbtbg7mo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/dashboard.js\n"));

/***/ })

});