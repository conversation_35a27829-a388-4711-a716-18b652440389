"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./pages/dashboard.js":
/*!****************************!*\
  !*** ./pages/dashboard.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    console.log(tech_data_user);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                href: \"#\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                            className: \"hidden md:block\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                children: \"My Skills Snapshot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"KNaG7HRFKj3fWN5peDVgbtbg7mo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2Rhc2hib2FyZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFDUztBQUNSO0FBS0o7QUFRRztBQUNrQjs7QUFFM0MsU0FBU2MsVUFBVSxLQUlqQztRQUppQyxFQUNoQ0Msa0JBQWtCLEVBQ2xCQyxnQkFBZ0IsRUFDaEJDLGNBQWMsRUFDZixHQUppQzs7SUFLaEMsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNvQixTQUFTQyxXQUFXLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQixVQUFVQyxZQUFZLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUV6QyxNQUFNd0IsU0FBU3RCLHNEQUFTQTtJQUV4QnVCLFFBQVFDLEdBQUcsQ0FBQ1Q7SUFFWmhCLGdEQUFTQTsrQkFBQztZQUNSLDZCQUE2QjtZQUM3QkUsMkRBQVFBLENBQUN3QixJQUFJLENBQUNDLFVBQVUsR0FBR0MsSUFBSTt1Q0FBQzt3QkFBQyxFQUFFQyxNQUFNLEVBQUVWLE9BQU8sRUFBRSxFQUFFO29CQUNwREMsV0FBV0Q7b0JBQ1gsSUFBSSxDQUFDQSxTQUFTO3dCQUNaLHVDQUF1Qzt3QkFDdkNJLE9BQU9PLElBQUksQ0FBQztvQkFDZDtnQkFDRjs7WUFFQSxnQ0FBZ0M7WUFDaEMsTUFBTSxFQUNKRCxNQUFNLEVBQUVFLFlBQVksRUFBRSxFQUN2QixHQUFHN0IsMkRBQVFBLENBQUN3QixJQUFJLENBQUNNLGlCQUFpQjt1Q0FBQyxDQUFDQyxRQUFRZDtvQkFDM0NDLFdBQVdEO29CQUNYLElBQUksQ0FBQ0EsU0FBUzt3QkFDWiw0Q0FBNEM7d0JBQzVDSSxPQUFPTyxJQUFJLENBQUM7b0JBQ2Q7Z0JBQ0Y7O1lBRUE7dUNBQU8sSUFBTUMsYUFBYUcsV0FBVzs7UUFDdkM7OEJBQUc7UUFBQ1g7S0FBTztJQUVYLCtDQUErQztJQUMvQ3ZCLGdEQUFTQTsrQkFBQztZQUNSLElBQUltQixTQUFTO2dCQUNYZ0I7WUFDRjtRQUNGOzhCQUFHO1FBQUNoQjtLQUFRO0lBRVosaUJBQWlCLEdBQ2pCLHFDQUFxQztJQUNyQyxtQ0FBbUM7SUFFbkMsbUNBQW1DO0lBQ25DLGlDQUFpQztJQUVqQyxpQkFBaUIsR0FFakIsZUFBZWdCO1FBQ2IsSUFBSTtZQUNGakIsV0FBVztZQUVYLE1BQU0sRUFDSlcsTUFBTSxFQUFFTyxJQUFJLEVBQUUsRUFDZixHQUFHLE1BQU1sQywyREFBUUEsQ0FBQ3dCLElBQUksQ0FBQ1csT0FBTztZQUUvQixJQUFJLENBQUNELE1BQU07Z0JBQ1QsTUFBTSxJQUFJRSxNQUFNO1lBQ2xCO1lBRUEsSUFBSSxFQUFFVCxJQUFJLEVBQUVVLEtBQUssRUFBRUMsTUFBTSxFQUFFLEdBQUcsTUFBTXRDLDJEQUFRQSxDQUN6Q3VDLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLE1BQU1QLEtBQUtRLEVBQUUsRUFDaEJDLE1BQU07WUFFVCxJQUFJTixTQUFTQyxXQUFXLEtBQUs7Z0JBQzNCLE1BQU1EO1lBQ1I7WUFFQSxJQUFJVixNQUFNO2dCQUNSUCxZQUFZTztZQUNkO1FBQ0YsRUFBRSxPQUFPVSxPQUFPO1lBQ2RmLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0NjLE1BQU1PLE9BQU87UUFDekQsd0VBQXdFO1FBQ3hFLGdDQUFnQztRQUNsQyxTQUFVO1lBQ1I1QixXQUFXO1FBQ2I7SUFDRjtJQUVBLG1EQUFtRDtJQUNuRCxJQUFJRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUM4QjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUM3QyxtREFBSUE7d0JBQUM4QyxRQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLdkI7SUFFQSx1REFBdUQ7SUFDdkQsSUFBSSxDQUFDOUIsU0FBUztRQUNaLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDZix3RUFBZUE7OzBCQUNkLDhEQUFDUSxvRUFBVUE7Z0JBQ1RTLFVBQVVBO2dCQUNWUCxvQkFBb0JBO2dCQUNwQkMsa0JBQWtCQTs7Ozs7OzBCQUdwQiw4REFBQ1QscUVBQVlBOzBCQUNYLDRFQUFDNEM7b0JBQU9GLFdBQVU7OEJBQ2hCLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUMzQyx1RUFBY0E7Ozs7OzBDQUVmLDhEQUFDRSxzRUFBVUE7MENBQ1QsNEVBQUNHLDBFQUFjQTs7c0RBQ2IsOERBQUNGLDBFQUFjQTs0Q0FBQ3dDLFdBQVU7c0RBQ3hCLDRFQUFDdkMsMEVBQWNBO2dEQUFDMEMsTUFBSzswREFBSTs7Ozs7Ozs7Ozs7c0RBRTNCLDhEQUFDeEMsK0VBQW1CQTs0Q0FBQ3FDLFdBQVU7Ozs7OztzREFDL0IsOERBQUN4QywwRUFBY0E7c0RBQ2IsNEVBQUNDLDBFQUFjQTswREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTbEM7R0F0SXdCSTs7UUFTUFosa0RBQVNBOzs7S0FURlkiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yaWphc3QvX19kYXRhL19fZGV2L0VxdWFsaXRhbC9kZXYvZGNjL2RjYy1hcHAvZGNjLWFwcC9wYWdlcy9kYXNoYm9hcmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvcm91dGVyXCI7XG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gXCJAdXRpbHMvc3VwYWJhc2VDbGllbnRcIjtcbmltcG9ydCB7IE92YWwgfSBmcm9tIFwic3ZnLWxvYWRlcnMtcmVhY3RcIjtcbmltcG9ydCB7XG4gIFNpZGViYXJQcm92aWRlcixcbiAgU2lkZWJhclRyaWdnZXIsXG4gIFNpZGViYXJJbnNldCxcbn0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xuXG5pbXBvcnQge1xuICBCcmVhZGNydW1iLFxuICBCcmVhZGNydW1iSXRlbSxcbiAgQnJlYWRjcnVtYkxpbmssXG4gIEJyZWFkY3J1bWJMaXN0LFxuICBCcmVhZGNydW1iU2VwYXJhdG9yLFxufSBmcm9tIFwiQGNvcmUvY29tcG9uZW50cy91aS9icmVhZGNydW1iXCI7XG5pbXBvcnQgeyBBcHBTaWRlYmFyIH0gZnJvbSBcIkBjb3JlL2NvbXBvbmVudHMvYXBwLXNpZGViYXJcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkKHtcbiAgYmVoYXZpb3VyYWxfc2tpbGxzLFxuICB0ZWNobmljYWxfc2tpbGxzLFxuICB0ZWNoX2RhdGFfdXNlcixcbn0pIHtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzZXNzaW9uLCBzZXRTZXNzaW9uXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbdXNlckRhdGEsIHNldFVzZXJEYXRhXSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIGNvbnNvbGUubG9nKHRlY2hfZGF0YV91c2VyKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIENoZWNrIGZvciBleGlzdGluZyBzZXNzaW9uXG4gICAgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCkudGhlbigoeyBkYXRhOiB7IHNlc3Npb24gfSB9KSA9PiB7XG4gICAgICBzZXRTZXNzaW9uKHNlc3Npb24pO1xuICAgICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICAgIC8vIFJlZGlyZWN0IHRvIGluZGV4IHBhZ2UgaWYgbm8gc2Vzc2lvblxuICAgICAgICByb3V0ZXIucHVzaChcIi9cIik7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICAvLyBMaXN0ZW4gZm9yIGF1dGggc3RhdGUgY2hhbmdlc1xuICAgIGNvbnN0IHtcbiAgICAgIGRhdGE6IHsgc3Vic2NyaXB0aW9uIH0sXG4gICAgfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoKF9ldmVudCwgc2Vzc2lvbikgPT4ge1xuICAgICAgc2V0U2Vzc2lvbihzZXNzaW9uKTtcbiAgICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgICAvLyBSZWRpcmVjdCB0byBpbmRleCBwYWdlIGlmIHNlc3Npb24gaXMgbG9zdFxuICAgICAgICByb3V0ZXIucHVzaChcIi9cIik7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gKCkgPT4gc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gIH0sIFtyb3V0ZXJdKTtcblxuICAvLyBGZXRjaCB1c2VyIHByb2ZpbGUgd2hlbiBzZXNzaW9uIGlzIGF2YWlsYWJsZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZXNzaW9uKSB7XG4gICAgICBnZXRVc2VyUHJvZmlsZSgpO1xuICAgIH1cbiAgfSwgW3Nlc3Npb25dKTtcblxuICAvKiAtLS0gREVCVUcgLS0tICovXG4gIC8vIGNvbnNvbGUubG9nKFwiYmVoYXZpb3VyYWxfc2tpbGxzXCIpO1xuICAvLyBjb25zb2xlLmxvZyhiZWhhdmlvdXJhbF9za2lsbHMpO1xuXG4gIC8vIGNvbnNvbGUubG9nKFwidGVjaG5pY2FsX3NraWxsc1wiKTtcbiAgLy8gY29uc29sZS5sb2codGVjaG5pY2FsX3NraWxscyk7XG5cbiAgLyogLS0tIERFQlVHIC0tLSAqL1xuXG4gIGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJQcm9maWxlKCkge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgICBjb25zdCB7XG4gICAgICAgIGRhdGE6IHsgdXNlciB9LFxuICAgICAgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gdXNlciBmb3VuZFwiKTtcbiAgICAgIH1cblxuICAgICAgbGV0IHsgZGF0YSwgZXJyb3IsIHN0YXR1cyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oXCJwcm9maWxlc1wiKVxuICAgICAgICAuc2VsZWN0KFwiKlwiKVxuICAgICAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChlcnJvciAmJiBzdGF0dXMgIT09IDQwNikge1xuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cblxuICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgc2V0VXNlckRhdGEoZGF0YSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKFwiRXJyb3IgZmV0Y2hpbmcgdXNlciBwcm9maWxlOlwiLCBlcnJvci5tZXNzYWdlKTtcbiAgICAgIC8vIElmIHRoZXJlJ3MgYW4gZXJyb3IgZmV0Y2hpbmcgcHJvZmlsZSwgd2UgY2FuIHN0aWxsIHNob3cgdGhlIGRhc2hib2FyZFxuICAgICAgLy8gYnV0IHVzZXJEYXRhIHdpbGwgcmVtYWluIG51bGxcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHN0YXRlIHdoaWxlIGNoZWNraW5nIGF1dGhlbnRpY2F0aW9uXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIHBsYWNlLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTAgbWItMTAgZmxleCBmbGV4LXJvdyBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxPdmFsIHN0cm9rZT1cIiMwYzM5YWNcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgZGFzaGJvYXJkIGlmIG5vIHNlc3Npb24gKHdpbGwgcmVkaXJlY3QpXG4gIGlmICghc2Vzc2lvbikge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8U2lkZWJhclByb3ZpZGVyPlxuICAgICAgPEFwcFNpZGViYXJcbiAgICAgICAgdXNlckRhdGE9e3VzZXJEYXRhfVxuICAgICAgICBiZWhhdmlvdXJhbF9za2lsbHM9e2JlaGF2aW91cmFsX3NraWxsc31cbiAgICAgICAgdGVjaG5pY2FsX3NraWxscz17dGVjaG5pY2FsX3NraWxsc31cbiAgICAgIC8+XG5cbiAgICAgIDxTaWRlYmFySW5zZXQ+XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBoLTE2IHNocmluay0wIGl0ZW1zLWNlbnRlciBnYXAtMiBib3JkZXItYlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtM1wiPlxuICAgICAgICAgICAgPFNpZGViYXJUcmlnZ2VyIC8+XG5cbiAgICAgICAgICAgIDxCcmVhZGNydW1iPlxuICAgICAgICAgICAgICA8QnJlYWRjcnVtYkxpc3Q+XG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxuICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rIGhyZWY9XCIjXCI+RGFzaGJvYXJkPC9CcmVhZGNydW1iTGluaz5cbiAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiIC8+XG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rPk15IFNraWxscyBTbmFwc2hvdDwvQnJlYWRjcnVtYkxpbms+XG4gICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cbiAgICAgICAgICAgICAgPC9CcmVhZGNydW1iTGlzdD5cbiAgICAgICAgICAgIDwvQnJlYWRjcnVtYj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9oZWFkZXI+XG4gICAgICA8L1NpZGViYXJJbnNldD5cbiAgICA8L1NpZGViYXJQcm92aWRlcj5cbiAgKTtcbn1cblxuLy8gZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFN0YXRpY1Byb3BzKCkge1xuLy8gICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuLy8gICAgIC5mcm9tKFwiYmVoYXZpb3VyYWxfc2tpbGxzXCIpXG4vLyAgICAgLnNlbGVjdChcbi8vICAgICAgIFwiaWQsIGdyb3VwLCBza2lsbF9uYW1lLCBiZWhhdmlvdXJhbF9zdWJfc2tpbGxzIChpZCwgc3ViX3NraWxsX25hbWUpXCJcbi8vICAgICApO1xuXG4vLyAgIGlmIChkYXRhKSB7XG4vLyAgICAgY29uc29sZS5sb2coZGF0YSk7XG4vLyAgIH1cblxuLy8gICBpZiAoZXJyb3IpIHtcbi8vICAgICBjb25zb2xlLmxvZyhlcnJvcik7XG4vLyAgIH1cblxuLy8gICBjb25zdCBiZWhhdmlvdXJhbF9za2lsbHMgPSBkYXRhO1xuXG4vLyAgIHJldHVybiB7XG4vLyAgICAgcHJvcHM6IHsgYmVoYXZpb3VyYWxfc2tpbGxzIH0sXG4vLyAgIH07XG4vLyB9XG5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgYmRhdGEgPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKFwiYmVoYXZpb3VyYWxfc2tpbGxzXCIpXG4gICAgLnNlbGVjdChcbiAgICAgIFwiaWQsIGdyb3VwLCBza2lsbF9uYW1lLCBiZWhhdmlvdXJhbF9zdWJfc2tpbGxzIChpZCwgc3ViX3NraWxsX25hbWUpXCJcbiAgICApO1xuXG4gIGNvbnN0IHRlY2hkYXRhID0gYXdhaXQgc3VwYWJhc2VcbiAgICAuZnJvbShcInRlY2huaWNhbF9za2lsbHNcIilcbiAgICAuc2VsZWN0KFwiaWQsIHNraWxsX25hbWUsIHRlY2huaWNhbF9zdWJfc2tpbGxzIChpZCwgc3ViX3NraWxsX25hbWUpXCIpO1xuXG4gIGNvbnN0IHRlY2hkYXRhVXNlciA9IGF3YWl0IHN1cGFiYXNlXG4gICAgLmZyb20oXCJ0ZWNobmljYWxfc2tpbGxzXCIpXG4gICAgLnNlbGVjdChcImlkLCBza2lsbF9uYW1lLCByb2xlX21hcHBpbmdzIChza2lsbF9sZXZlbCwgc2tpbGxfaWQpXCIpO1xuXG4gIGNvbnNvbGUubG9nKHRlY2hkYXRhVXNlcik7XG5cbiAgY29uc3QgcmVzcG9uc2VzID0gYXdhaXQgUHJvbWlzZS5hbGwoW2JkYXRhLCB0ZWNoZGF0YSwgdGVjaGRhdGFVc2VyXSk7XG5cbiAgLy8gY29uc29sZS5sb2cocmVzcG9uc2VzKTtcblxuICByZXR1cm4ge1xuICAgIHByb3BzOiB7XG4gICAgICBiZWhhdmlvdXJhbF9za2lsbHM6IHJlc3BvbnNlc1swXS5kYXRhLFxuICAgICAgdGVjaG5pY2FsX3NraWxsczogcmVzcG9uc2VzWzFdLmRhdGEsXG4gICAgICB0ZWNoX2RhdGFfdXNlcjogcmVzcG9uc2VzWzJdLmRhdGEsXG4gICAgICAvLyB0ZWNoX2RhdGFfdXNlcjogdGVjaGRhdGFVc2VyLmRhdGEsXG4gICAgfSxcbiAgfTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJzdXBhYmFzZSIsIk92YWwiLCJTaWRlYmFyUHJvdmlkZXIiLCJTaWRlYmFyVHJpZ2dlciIsIlNpZGViYXJJbnNldCIsIkJyZWFkY3J1bWIiLCJCcmVhZGNydW1iSXRlbSIsIkJyZWFkY3J1bWJMaW5rIiwiQnJlYWRjcnVtYkxpc3QiLCJCcmVhZGNydW1iU2VwYXJhdG9yIiwiQXBwU2lkZWJhciIsIkRhc2hib2FyZCIsImJlaGF2aW91cmFsX3NraWxscyIsInRlY2huaWNhbF9za2lsbHMiLCJ0ZWNoX2RhdGFfdXNlciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2Vzc2lvbiIsInNldFNlc3Npb24iLCJ1c2VyRGF0YSIsInNldFVzZXJEYXRhIiwicm91dGVyIiwiY29uc29sZSIsImxvZyIsImF1dGgiLCJnZXRTZXNzaW9uIiwidGhlbiIsImRhdGEiLCJwdXNoIiwic3Vic2NyaXB0aW9uIiwib25BdXRoU3RhdGVDaGFuZ2UiLCJfZXZlbnQiLCJ1bnN1YnNjcmliZSIsImdldFVzZXJQcm9maWxlIiwidXNlciIsImdldFVzZXIiLCJFcnJvciIsImVycm9yIiwic3RhdHVzIiwiZnJvbSIsInNlbGVjdCIsImVxIiwiaWQiLCJzaW5nbGUiLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwic3Ryb2tlIiwiaGVhZGVyIiwiaHJlZiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/dashboard.js\n"));

/***/ })

});