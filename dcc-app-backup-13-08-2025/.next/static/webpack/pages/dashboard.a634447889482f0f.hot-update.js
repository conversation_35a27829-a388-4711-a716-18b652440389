"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "(pages-dir-browser)/./pages/dashboard.js":
/*!****************************!*\
  !*** ./pages/dashboard.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Dashboard(param) {\n    let { behavioural_skills, technical_skills, tech_data_user } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Dashboard.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Dashboard.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Dashboard.useEffect\"]);\n            return ({\n                \"Dashboard.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    // Fetch user profile when session is available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ console.log(\"tech_data_user\");\n    console.log(tech_data_user);\n    console.log(\"userData\");\n    console.log(userData && userData.role);\n    // const filterTest = tech_data_user.filter(\n    //   (item) => item.role_mapping.role_id === userData && userData.role\n    // );\n    // console.log(\"filterTest\");\n    // console.log(filterTest);\n    // console.log(\"behavioural_skills\");\n    // console.log(behavioural_skills);\n    // console.log(\"technical_skills\");\n    // console.log(technical_skills);\n    // filter the array based on user role\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Show loading state while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid place-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10 mb-10 flex flex-row space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__.Oval, {\n                        stroke: \"#0c39ac\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render dashboard if no session (will redirect)\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__.AppSidebar, {\n                userData: userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                href: \"#\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {\n                                            className: \"hidden md:block\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                children: \"My Skills Snapshot\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/dashboard.js\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"KNaG7HRFKj3fWN5peDVgbtbg7mo=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/dashboard.js\n"));

/***/ })

});