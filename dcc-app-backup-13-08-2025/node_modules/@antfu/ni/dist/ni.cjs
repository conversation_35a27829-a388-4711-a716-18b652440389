'use strict';

const process = require('node:process');
const runner = require('./shared/ni.jp8Y2gho.cjs');
const fzf_es = require('./shared/ni.DWErt4DW.cjs');
require('node:path');
require('readline');
require('events');
require('node:module');
require('child_process');
require('path');
require('process');
require('stream');
require('node:fs');
require('os');
require('tty');
require('node:os');
require('fs');
require('fs/promises');
require('node:fs/promises');

function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const process__default = /*#__PURE__*/_interopDefaultCompat(process);

async function fetchNpmPackages(pattern) {
  const registryLink = (pattern2) => `https://registry.npmjs.com/-/v1/search?text=${pattern2}&size=35`;
  const terminalColumns = process__default.stdout?.columns || 80;
  try {
    const result = await fetch(registryLink(pattern)).then((res) => res.json());
    return result.objects.map(({ package: pkg }) => ({
      title: runner.formatPackageWithUrl(
        `${pkg.name.padEnd(30, " ")} ${runner.c.blue(`v${pkg.version}`)}`,
        pkg.links.repository ?? pkg.links.npm,
        terminalColumns
      ),
      value: pkg
    }));
  } catch {
    console.error("Error when fetching npm registry");
    process__default.exit(1);
  }
}

runner.runCli(async (agent, args, ctx) => {
  const isInteractive = args[0] === "-i";
  if (isInteractive) {
    let fetchPattern;
    if (args[1] && !args[1].startsWith("-")) {
      fetchPattern = args[1];
    } else {
      const { pattern } = await runner.prompts({
        type: "text",
        name: "pattern",
        message: "search for package"
      });
      fetchPattern = pattern;
    }
    if (!fetchPattern) {
      process__default.exitCode = 1;
      return;
    }
    const packages = await fetchNpmPackages(fetchPattern);
    if (!packages.length) {
      console.error("No results found");
      process__default.exitCode = 1;
      return;
    }
    const fzf = new fzf_es.Fzf(packages, {
      selector: (item) => item.title,
      casing: "case-insensitive"
    });
    const { dependency } = await runner.prompts({
      type: "autocomplete",
      name: "dependency",
      choices: packages,
      instructions: false,
      message: "choose a package to install",
      limit: 15,
      async suggest(input, choices) {
        const results = fzf.find(input);
        return results.map((r) => choices.find((c2) => c2.value === r.item.value));
      }
    });
    if (!dependency) {
      process__default.exitCode = 1;
      return;
    }
    args = runner.exclude(args, "-d", "-p", "-i");
    const canInstallPeers = ["npm", "pnpm"].includes(agent);
    const { mode } = await runner.prompts({
      type: "select",
      name: "mode",
      message: `install ${runner.c.yellow(dependency.name)} as`,
      choices: [
        {
          title: "prod",
          value: "",
          selected: true
        },
        {
          title: "dev",
          value: "-D"
        },
        {
          title: `peer`,
          value: "--save-peer",
          disabled: !canInstallPeers
        }
      ]
    });
    args.push(dependency.name, mode);
  }
  return runner.parseNi(agent, args, ctx);
});
