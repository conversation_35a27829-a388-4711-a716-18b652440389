import { r as runCli, p as parseNi } from './shared/ni.B_O3iAd3.mjs';
import 'node:path';
import 'node:process';
import 'readline';
import 'events';
import 'node:module';
import 'child_process';
import 'path';
import 'process';
import 'stream';
import 'node:fs';
import 'os';
import 'tty';
import 'node:os';
import 'fs';
import 'fs/promises';
import 'node:fs/promises';

runCli(
  (agent, args, hasLock) => parseNi(agent, [...args, "--frozen-if-present"], hasLock),
  { autoInstall: true }
);
