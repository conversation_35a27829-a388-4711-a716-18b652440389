export { A as AGENTS, C as CLI_TEMP_DIR, u as COMMANDS, I as INSTALL_PAGE, L as LOCKS, U as UnsupportedCommand, o as cmdExists, v as constructCommand, d as detect, n as exclude, t as formatPackageWithUrl, k as getCliCommand, c as getCommand, g as getConfig, a as getDefaultAgent, b as getGlobalAgent, q as limitText, j as parseNa, p as parseNi, i as parseNlx, e as parseNr, f as parseNu, h as parseNun, m as remove, x as resolveCommand, l as run, r as runCli, s as serializeCommand, w as writeFileSafe } from './shared/ni.B_O3iAd3.mjs';
import 'node:path';
import 'node:process';
import 'readline';
import 'events';
import 'node:module';
import 'child_process';
import 'path';
import 'process';
import 'stream';
import 'node:fs';
import 'os';
import 'tty';
import 'node:os';
import 'fs';
import 'fs/promises';
import 'node:fs/promises';
