'use strict';

const runner = require('./shared/ni.jp8Y2gho.cjs');
require('node:path');
require('node:process');
require('readline');
require('events');
require('node:module');
require('child_process');
require('path');
require('process');
require('stream');
require('node:fs');
require('os');
require('tty');
require('node:os');
require('fs');
require('fs/promises');
require('node:fs/promises');

runner.runCli(
  (agent, args, hasLock) => runner.parseNi(agent, [...args, "--frozen-if-present"], hasLock),
  { autoInstall: true }
);
