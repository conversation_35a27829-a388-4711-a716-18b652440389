'use strict';

const process = require('node:process');
const runner = require('./shared/ni.jp8Y2gho.cjs');
const fzf_es = require('./shared/ni.DWErt4DW.cjs');
const fs = require('./shared/ni.DWRIF_6-.cjs');
require('node:path');
require('readline');
require('events');
require('node:module');
require('child_process');
require('path');
require('process');
require('stream');
require('node:fs');
require('os');
require('tty');
require('node:os');
require('fs');
require('fs/promises');
require('node:fs/promises');

function _interopDefaultCompat (e) { return e && typeof e === 'object' && 'default' in e ? e.default : e; }

const process__default = /*#__PURE__*/_interopDefaultCompat(process);

runner.runCli(async (agent, args, ctx) => {
  const isInteractive = !args.length && !ctx?.programmatic;
  if (isInteractive || args[0] === "-m") {
    const pkg = fs.getPackageJSON(ctx);
    const allDependencies = { ...pkg.dependencies, ...pkg.devDependencies };
    const raw = Object.entries(allDependencies);
    if (!raw.length) {
      console.error("No dependencies found");
      return;
    }
    const fzf = new fzf_es.Fzf(raw, {
      selector: ([dep, version]) => `${dep} ${version}`,
      casing: "case-insensitive"
    });
    const choices = raw.map(([dependency, version]) => ({
      title: dependency,
      value: dependency,
      description: version
    }));
    const isMultiple = args[0] === "-m";
    const type = isMultiple ? "autocompleteMultiselect" : "autocomplete";
    if (isMultiple)
      args = runner.exclude(args, "-m");
    try {
      const { depsToRemove } = await runner.prompts({
        type,
        name: "depsToRemove",
        choices,
        instructions: false,
        message: `remove ${isMultiple ? "dependencies" : "dependency"}`,
        async suggest(input, choices2) {
          const results = fzf.find(input);
          return results.map((r) => choices2.find((c) => c.value === r.item[0]));
        }
      });
      if (!depsToRemove) {
        process__default.exitCode = 1;
        return;
      }
      const isSingleDependency = typeof depsToRemove === "string";
      if (isSingleDependency)
        args.push(depsToRemove);
      else args.push(...depsToRemove);
    } catch {
      process__default.exit(1);
    }
  }
  return runner.parseNun(agent, args);
});
